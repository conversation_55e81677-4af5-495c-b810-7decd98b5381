import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/http_util.dart';

/// 奖励规则页面
class AwardRule extends StatefulWidget {
  const AwardRule({super.key});

  @override
  State<AwardRule> createState() => _AwardRuleState();
}

class _AwardRuleState extends State<AwardRule> {
  Future loadAwardRule() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Article";
    parameters["params"] = {"type": "Reward Rules"};
    var response = await HttpUtil.instance
        .post("/v1/public/query", parameters: parameters);
    if (response["code"] == 200) {
      var list = response["data"]["list"];
      if (list.length > 0) {
        var articleId = list[0]["id"];
        return loadArticle(articleId);
      }
    } else {
      return null;
    }
  }

  //获取服务列表
  Future loadArticle(int id) async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "ArticleDetail";
    var params = {};
    if (id > 0) {
      params["id"] = id.toString();
    }
    parameters["params"] = params;
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).awardRule),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: FutureBuilder(future: loadAwardRule(), builder: _buildFuture),
      ),
    );
  }

  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      String html = "";
      if (snapshot.data["code"] == 200) {
        List list = snapshot.data["data"]["list"];
        if (list.isNotEmpty) {
          html = list[0]["content"];
        }
      }
      return Column(
        children: [
          Expanded(child: SingleChildScrollView(child: Html(data: html))),
          Container(
            padding: const EdgeInsets.only(top: 28.0),
            margin:
                const EdgeInsets.only(left: 20.0, right: 20.0, bottom: 20.0),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(18.0),
                      backgroundColor:
                          const Color(0xFF224195), // Background color
                    ),
                    child: Text(
                      S.of(context).backToHome,
                      style: const TextStyle(color: Colors.white),
                    ),
                    onPressed: () async {
                      context.go(RoutePaths.home);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }
}
