import 'package:flutter/material.dart';
import 'package:new_solidcare/constant/app_colors.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:intl/intl.dart';

/// 提现记录页面
class ReminderRecord extends StatefulWidget {
  const ReminderRecord({super.key});

  @override
  State<ReminderRecord> createState() => _ReminderRecord();
}

class _ReminderRecord extends State<ReminderRecord>
    with SingleTickerProviderStateMixin {
  String lang = "en";
  int pageIndex = 1;
  bool isLoading = false; // 添加加载状态标记
  bool hasMore = true; // 添加是否有更多数据标记
  List recordList = []; // 存储所有记录
  final ScrollController _scrollController = ScrollController(); // 添加滚动控制器

  @override
  void initState() {
    super.initState();
    var localLang = Intl.getCurrentLocale();
    setState(() {
      if (localLang == "zh_CN") {
        lang = "zh";
      } else if (localLang == "zh_HK") {
        lang = "es";
      }
    });

    // 添加滚动监听
    _scrollController.addListener(_onScroll);
    // 初始加载数据
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 释放滚动控制器
    super.dispose();
  }

  // 滚动监听方法
  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    // 当距离底部不足 200 像素时加载更多
    if (currentScroll >= (maxScroll - 200) && !isLoading && hasMore) {
      _loadMore();
    }
  }

  // 加载更多数据
  Future<void> _loadMore() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
      pageIndex++;
    });

    await _loadData();
  }

  // 初始加载或刷新数据
  Future<void> _loadData() async {
    try {
      Map<String, dynamic> parameters = {
        "query": "OrderList",
        "page": pageIndex,
        "limit": 10,
      };

      final result = await HttpUtil.instance
          .post("/v1/withdrawal/order/query", parameters: parameters);

      if (result != null && result["code"] == 200) {
        List newList = result["data"]["list"] ?? [];
        int total = result["data"]["total"] ?? 0;

        setState(() {
          if (pageIndex == 1) {
            recordList = newList;
          } else {
            recordList.addAll(newList);
          }

          // 判断是否还有更多数据
          hasMore = recordList.length < total;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).withdrawRecord),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: RefreshIndicator(
          // 添加下拉刷新
          onRefresh: () async {
            setState(() {
              pageIndex = 1;
              hasMore = true;
            });
            await _loadData();
          },
          child: ListView(
            controller: _scrollController,
            children: [
              getPointsList(recordList),
              if (isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
              if (!hasMore && recordList.isNotEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(S.of(context).noMoreData),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getPointsList(List pointsData) {
    // 0 申请中 1 已审核 2 已支付 3 作废
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        children: pointsData.asMap().entries.map((entry) {
          // 根据颜色选择字体颜色
          var point = entry.value; // 获取当前元素
          var state = point['state']!;
          var statusStr = S.of(context).applying;
          if (state == 0) {
            statusStr = S.of(context).applying;
          } else if (state == 1) {
            statusStr = S.of(context).reviewed;
          } else if (state == 2) {
            statusStr = S.of(context).paid;
          } else if (state == 3) {
            statusStr = S.of(context).cancelled;
          }
          return Container(
            margin: const EdgeInsets.only(bottom: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${S.of(context).account}${point['name']!}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${S.of(context).submitTime}${point['subTime']!}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${S.of(context).paymentTime}${point['updatedTime']!}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${S.of(context).withdrawalPoints}${point['integral']!}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${S.of(context).cashAmount}${point['amount']!}',
                      style: const TextStyle(
                          color: AppColors.COLOR_PRIMARY,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Visibility(
                  visible: point['state'] != 2,
                  child: Text(
                    statusStr,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                Visibility(
                  visible: point['state'] == 2,
                  child: Image.asset(
                    lang == "zh"
                        ? 'images/play_ok.png'
                        : 'images/paid_blue.png',
                    width: 78,
                    height: 78,
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
