import 'package:flutter/material.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/utils/http_util.dart';

/// 好友列表页面
class Friends extends StatefulWidget {
  const Friends({super.key});

  @override
  State<Friends> createState() => _FriendsState();
}

class _FriendsState extends State<Friends> with SingleTickerProviderStateMixin {
  String customerName = "";
  int pageIndex = 1;
  bool isLoading = false;
  bool hasMore = true;
  List friendsList = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 添加滚动监听
    _scrollController.addListener(_onScroll);
    // 初始加载数据
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    if (currentScroll >= (maxScroll - 200) && !isLoading && hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
      pageIndex++;
    });

    await _loadData();
  }

  // 处理搜索
  void handleSearch() {
    setState(() {
      pageIndex = 1;
      hasMore = true;
      friendsList.clear();
    });
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      Map<String, dynamic> parameters = {
        "query": "ShareList",
        "page": pageIndex,
        "limit": 10,
        "params": {
          "customerName": customerName.trim(),
        }
      };

      final result = await HttpUtil.instance
          .post("/v1/customer/query", parameters: parameters);

      if (result != null && result["code"] == 200) {
        List newList = result["data"]["list"] ?? [];
        int total = result["data"]["total"] ?? 0;

        setState(() {
          if (pageIndex == 1) {
            friendsList = newList;
          } else {
            friendsList.addAll(newList);
          }

          hasMore = friendsList.length < total;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).friendsList),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            setState(() {
              pageIndex = 1;
              hasMore = true;
              friendsList.clear();
            });
            await _loadData();
          },
          child: ListView(
            controller: _scrollController,
            children: [
              _buildSearchBar(),
              if (friendsList.isNotEmpty) getTopPointsInfo(friendsList.length),
              getPointsList(friendsList),
              if (isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
              if (!hasMore && friendsList.isNotEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(S.of(context).noMoreData),
                  ),
                ),
              // 添加底部空间
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget getTopPointsInfo(int total) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            S.of(context).friendsCount,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
          ),
          Text(
            total.toString(),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget getPointsList(List pointsData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      margin: const EdgeInsets.only(top: 10),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: pointsData.asMap().entries.map((entry) {
          // 根据颜色选择字体颜色
          int index = entry.key; // 获取索引
          var point = entry.value; // 获取当前元素
          Color borderColor = index == pointsData.length - 1
              ? const Color.fromARGB(255, 255, 255, 255)
              : const Color(0xFFE5E5E5);
          return Container(
            margin: const EdgeInsets.only(bottom: 8.0),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: borderColor,
                  width: 1.0,
                ),
              ),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 60,
                  child: Icon(
                    Icons.people,
                    color: Colors.blueAccent,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${point['createdTime']!} ${S.of(context).register}",
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        point['customerName']!,
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      margin: const EdgeInsets.only(bottom: 8, top: 8),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, color: Colors.grey, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      onChanged: (value) {
                        setState(() {
                          customerName = value; // 更新搜索关键字
                        });
                      },
                      decoration: InputDecoration(
                        hintText: S.of(context).search,
                        border: InputBorder.none,
                        hintStyle: const TextStyle(fontSize: 14),
                        contentPadding: const EdgeInsets.only(bottom: 10),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 6),
          TextButton(
            onPressed: handleSearch, // 添加搜索处理
            child: Text(
              S.of(context).search,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
