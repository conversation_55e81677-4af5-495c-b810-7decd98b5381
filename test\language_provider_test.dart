import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:new_solidcare/providers/language_provider.dart';

void main() {
  group('LanguageProvider Tests', () {
    late LanguageProvider languageProvider;

    setUp(() {
      languageProvider = LanguageProvider();
    });

    test('should have default locale as English', () {
      expect(languageProvider.currentLocale.languageCode, 'en');
    });

    test('should get correct display name for current language', () {
      expect(languageProvider.currentLanguageDisplayName, 'English');
    });

    test('should get correct index for current language', () {
      expect(languageProvider.currentLanguageIndex, 0);
    });
  });
}
