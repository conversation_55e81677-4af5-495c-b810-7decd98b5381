import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:new_solidcare/providers/language_provider.dart';
import 'package:new_solidcare/utils/language_util.dart';

void main() {
  group('Refresh Language Tests', () {
    testWidgets('should show loading screen when not initialized',
        (WidgetTester tester) async {
      final languageProvider = LanguageProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider<LanguageProvider>.value(
          value: languageProvider,
          child: Consumer<LanguageProvider>(
            builder: (context, provider, child) {
              if (!provider.isInitialized) {
                return const MaterialApp(
                  home: Scaffold(
                    body: Center(
                      child: Text('Initializing...'),
                    ),
                  ),
                );
              }
              return const MaterialApp(
                home: Scaffold(
                  body: Center(
                    child: Text('App Ready'),
                  ),
                ),
              );
            },
          ),
        ),
      );

      // 应该显示初始化界面
      expect(find.text('Initializing...'), findsOneWidget);
      expect(find.text('App Ready'), findsNothing);
    });

    testWidgets('should show app when initialized',
        (WidgetTester tester) async {
      final languageProvider = LanguageProvider();

      // 创建一个简化的测试Widget
      await tester.pumpWidget(
        ChangeNotifierProvider<LanguageProvider>.value(
          value: languageProvider,
          child: MaterialApp(
            home: Consumer<LanguageProvider>(
              builder: (context, provider, child) {
                if (!provider.isInitialized) {
                  return const Scaffold(
                    body: Center(
                      child: Text('Initializing...'),
                    ),
                  );
                }
                return const Scaffold(
                  body: Center(
                    child: Text('App Ready'),
                  ),
                );
              },
            ),
          ),
        ),
      );

      // 初始状态应该显示初始化界面
      expect(find.text('Initializing...'), findsOneWidget);
      expect(find.text('App Ready'), findsNothing);

      // 手动触发初始化
      await languageProvider.initializeLanguage();

      // 等待UI更新
      await tester.pumpAndSettle();

      // 现在应该显示应用界面
      expect(find.text('App Ready'), findsOneWidget);
      expect(find.text('Initializing...'), findsNothing);
    });

    test('should maintain language after initialization', () async {
      final languageProvider = LanguageProvider();

      // 初始化前应该是默认语言
      expect(languageProvider.currentLocale.languageCode, 'en');
      expect(languageProvider.isInitialized, false);

      // 初始化后
      await languageProvider.initializeLanguage();
      expect(languageProvider.isInitialized, true);

      // 语言应该保持一致（根据系统语言或保存的设置）
      expect(
          LanguageUtil.supportedLanguages
                  .contains(languageProvider.currentLocale.languageCode) ||
              languageProvider.currentLocale.toString().startsWith('zh'),
          true);
    });
  });
}
