import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import '../../generated/l10n.dart';
import '../../utils/http_util.dart';
import 'package:flutter_html/flutter_html.dart';

class ArticleDetailPage extends StatefulWidget {
  const ArticleDetailPage(
      {super.key, required this.articleId, required this.articleType});
  final int articleId; //文章ID
  final String articleType; //文章类型
  @override
  State<ArticleDetailPage> createState() => _ArticleDetailState();
}

class _ArticleDetailState extends State<ArticleDetailPage> {
  @override
  void initState() {
    super.initState();
  }

  String articleTitle = "";
  bool isShare = false;
  //获取服务列表
  Future loadProduct() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "ArticleDetail";
    var params = {};
    if (widget.articleId > 0) {
      params["id"] = widget.articleId.toString();
    }
    if (widget.articleType != "" && widget.articleType.isNotEmpty) {
      params["articleType"] = widget.articleType;
    }
    parameters["params"] = params;
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  //构造swiper
  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      String html = "";
      if (snapshot.data["code"] == 200) {
        List list = snapshot.data["data"]["list"];
        if (list.isNotEmpty) {
          articleTitle = list[0]["title"];
          html = list[0]["content"];
          isShare = list[0]["isShare"] == 1;
        }
      }
      if (widget.articleType == "blog") {
        return _buildBlog();
      } else {
        return _buildArticle(html);
      }
    } else {
      return _buildLoading();
    }
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return FutureBuilder(future: loadProduct(), builder: _buildFuture);
  }

  Widget _buildBlog() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(S.of(context).selfTitle),
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Builder(
        builder: (BuildContext context) {
          return WebViewX(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            initialContent: "http://www.solidcarehomeimprovements.ca",
          );
        },
      ),
    );
  }

  Widget _buildArticle(html) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 90,
        backgroundColor: Colors.transparent,
        title: Text(articleTitle),
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Builder(
        builder: (BuildContext context) {
          return SingleChildScrollView(
            padding: const EdgeInsets.only(top: 20),
            child: Html(data: html),
          );
        },
      ),
      floatingActionButton: Visibility(
        visible: isShare,
        child: Align(
          alignment: Alignment.centerRight,
          child: InkWell(
            onTap: () {
              context.pushNamed(RouteNames.shared);
            },
            borderRadius: BorderRadius.circular(30), // 设置圆角
            child: Container(
              width: 100,
              height: 42,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                  topRight: Radius.circular(0), // 右上角不圆角
                  bottomRight: Radius.circular(0), // 右下角不圆角
                ),
              ),
              child: Center(
                child: Text(
                  S.of(context).shareEarn,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 90,
        backgroundColor: Colors.transparent,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Center(
        child: Text(S.of(context).loading),
      ),
    );
  }
}
