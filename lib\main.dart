import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:wonderpush_flutter/wonderpush_flutter.dart';
import 'generated/l10n.dart';
import 'view_model/apointment_model.dart';
import 'package:flutter/foundation.dart';
import 'view_model/main_model.dart';
import 'utils/language_util.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化语言设置
  await _initializeLocale();

  var apointmentModel = ApointmentModel();
  var mainModel = MainModel();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(MultiProvider(
      providers: [
        ChangeNotifierProvider<ApointmentModel>.value(value: apointmentModel),
        ChangeNotifierProvider<MainModel>.value(value: mainModel),
      ],
      child: const MyApp(),
    ));
    WonderPush.subscribeToNotifications();
    if (kDebugMode) {
      WonderPush.setLogging(true);
    }
  });
  //runApp(const MyApp());
  // 忽略特定的渲染错误
  // FlutterError.onError = (FlutterErrorDetails details) {
  //   if (!details.toString().contains('renderObject')) {
  //     FlutterError.presentError(details);
  //   }
  // };
}

/// 初始化语言设置
Future<void> _initializeLocale() async {
  try {
    // 使用语言工具类初始化语言设置
    String targetLanguage = await LanguageUtil.initializeLanguage();

    debugPrint("targetLanguage: $targetLanguage");

    // 初始化国际化
    Locale locale = Locale(targetLanguage);
    await S.load(locale);

    if (kDebugMode) {
      print('Language initialized: $targetLanguage');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing locale: $e');
    }
    // 如果出错，默认使用英语
    await S.load(const Locale('en'));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: router,
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: S.delegate.supportedLocales,
      title: 'SolidCare',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        appBarTheme: const AppBarTheme(
          // backgroundColor: Colors.blue, // 全局设置 AppBar 的背景色
          iconTheme: IconThemeData(color: Colors.white), // 全局设置返回按钮和其他图标颜色为白色
          titleTextStyle: TextStyle(
            color: Colors.white, // 全局设置标题颜色为白色
            fontSize: 20, // 可以根据需要设置字体大小
          ),
        ),
      ),
    );
  }
}
