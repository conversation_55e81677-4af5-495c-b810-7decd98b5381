import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:wonderpush_flutter/wonderpush_flutter.dart';
import 'generated/l10n.dart';
import 'view_model/apointment_model.dart';
import 'package:flutter/foundation.dart';
import 'view_model/main_model.dart';
import 'providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  var apointmentModel = ApointmentModel();
  var mainModel = MainModel();
  var languageProvider = LanguageProvider();

  // 确保在运行应用前完成语言初始化
  await languageProvider.initializeLanguage();

  // 设置屏幕方向
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // 运行应用
  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider<ApointmentModel>.value(value: apointmentModel),
      ChangeNotifierProvider<MainModel>.value(value: mainModel),
      ChangeNotifierProvider<LanguageProvider>.value(value: languageProvider),
    ],
    child: const MyApp(),
  ));

  // 初始化推送服务
  WonderPush.subscribeToNotifications();
  if (kDebugMode) {
    WonderPush.setLogging(true);
  }

  //runApp(const MyApp());
  // 忽略特定的渲染错误
  // FlutterError.onError = (FlutterErrorDetails details) {
  //   if (!details.toString().contains('renderObject')) {
  //     FlutterError.presentError(details);
  //   }
  // };
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        // 确保语言已经初始化
        if (!languageProvider.isInitialized) {
          return MaterialApp(
            home: Scaffold(
              body: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFF224195),
                      Color(0xFF344C9C),
                    ],
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.home_repair_service,
                        size: 80,
                        color: Colors.white,
                      ),
                      SizedBox(height: 20),
                      Text(
                        'SolidCare',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 40),
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      SizedBox(height: 20),
                      Text(
                        'Initializing...',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }

        // 语言已初始化，显示正常应用
        return MaterialApp.router(
          routerConfig: router,
          locale: languageProvider.currentLocale,
          localizationsDelegates: const [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
          title: 'SolidCare',
          theme: ThemeData(
            primarySwatch: Colors.blue,
            appBarTheme: const AppBarTheme(
              // backgroundColor: Colors.blue, // 全局设置 AppBar 的背景色
              iconTheme:
                  IconThemeData(color: Colors.white), // 全局设置返回按钮和其他图标颜色为白色
              titleTextStyle: TextStyle(
                color: Colors.white, // 全局设置标题颜色为白色
                fontSize: 20, // 可以根据需要设置字体大小
              ),
            ),
          ),
        );
      },
    );
  }
}
