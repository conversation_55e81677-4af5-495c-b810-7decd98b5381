import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:wonderpush_flutter/wonderpush_flutter.dart';
import 'generated/l10n.dart';
import 'view_model/apointment_model.dart';
import 'package:flutter/foundation.dart';
import 'view_model/main_model.dart';
import 'providers/language_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  var apointmentModel = ApointmentModel();
  var mainModel = MainModel();
  var languageProvider = LanguageProvider();

  // 初始化语言设置
  await languageProvider.initializeLanguage();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
      .then((_) {
    runApp(MultiProvider(
      providers: [
        ChangeNotifierProvider<ApointmentModel>.value(value: apointmentModel),
        ChangeNotifierProvider<MainModel>.value(value: mainModel),
        ChangeNotifierProvider<LanguageProvider>.value(value: languageProvider),
      ],
      child: const MyApp(),
    ));
    WonderPush.subscribeToNotifications();
    if (kDebugMode) {
      WonderPush.setLogging(true);
    }
  });
  //runApp(const MyApp());
  // 忽略特定的渲染错误
  // FlutterError.onError = (FlutterErrorDetails details) {
  //   if (!details.toString().contains('renderObject')) {
  //     FlutterError.presentError(details);
  //   }
  // };
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return MaterialApp.router(
          routerConfig: router,
          locale: languageProvider.currentLocale,
          localizationsDelegates: const [
            S.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
          title: 'SolidCare',
          theme: ThemeData(
            primarySwatch: Colors.blue,
            appBarTheme: const AppBarTheme(
              // backgroundColor: Colors.blue, // 全局设置 AppBar 的背景色
              iconTheme:
                  IconThemeData(color: Colors.white), // 全局设置返回按钮和其他图标颜色为白色
              titleTextStyle: TextStyle(
                color: Colors.white, // 全局设置标题颜色为白色
                fontSize: 20, // 可以根据需要设置字体大小
              ),
            ),
          ),
        );
      },
    );
  }
}
