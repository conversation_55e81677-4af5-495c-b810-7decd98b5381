PODS:
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WonderPush (4.3.1):
    - WonderPush/Core (= 4.3.1)
  - WonderPush/Core (4.3.1)
  - wonderpush_flutter (4.3.1):
    - Flutter
    - WonderPush (= 4.3.1)
  - WonderPushExtension (4.3.1):
    - WonderPushExtension/Extension (= 4.3.1)
  - WonderPushExtension/Extension (4.3.1)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - wonderpush_flutter (from `.symlinks/plugins/wonderpush_flutter/ios`)
  - WonderPushExtension (~> 4.0)

SPEC REPOS:
  trunk:
    - Toast
    - WonderPush
    - WonderPushExtension

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  wonderpush_flutter:
    :path: ".symlinks/plugins/wonderpush_flutter/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  WonderPush: 457eb234758acec58ecd6c232d48c68e05dcb392
  wonderpush_flutter: 2f9e577775aacefc113b9552e2ee72e845c55b47
  WonderPushExtension: b4f1419a6dcaf16a3d2f02f27dafc8de0fcc0fd1

PODFILE CHECKSUM: 2fd92fe9e0bf5984d6a494fe27f293250969027c

COCOAPODS: 1.16.2
