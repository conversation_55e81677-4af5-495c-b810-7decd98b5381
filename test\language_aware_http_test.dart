import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:new_solidcare/providers/language_provider.dart';
import 'package:new_solidcare/services/language_aware_http_service.dart';

void main() {
  group('LanguageAwareHttpService Tests', () {
    late LanguageAwareHttpService httpService;
    late LanguageProvider languageProvider;

    setUp(() {
      httpService = LanguageAwareHttpService();
      languageProvider = LanguageProvider();
    });

    testWidgets('should get correct language from provider',
        (WidgetTester tester) async {
      // 创建一个测试Widget
      await tester.pumpWidget(
        ChangeNotifierProvider<LanguageProvider>.value(
          value: languageProvider,
          child: MaterialApp(
            home: Builder(
              builder: (BuildContext context) {
                // 测试从Provider获取语言
                String lang =
                    httpService.getCurrentLanguageFromProvider(context);
                expect(lang, 'en'); // 默认应该是英语

                return Container();
              },
            ),
          ),
        ),
      );
    });

    // 注意：_convertToApiLanguage是私有方法，我们通过公共方法测试
    test('should handle language conversion through public methods', () {
      // 这里我们可以通过其他方式测试语言转换逻辑
      String defaultLang = httpService.getCurrentLanguageFromProvider(null);
      expect(defaultLang, 'en');
    });

    test('should return default language when context is null', () {
      String lang = httpService.getCurrentLanguageFromProvider(null);
      expect(lang, 'en');
    });
  });
}
