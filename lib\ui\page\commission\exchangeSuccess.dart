import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/http_util.dart';

// 兑换确认页
class ExchangeSuccessPage extends StatefulWidget {
  const ExchangeSuccessPage({super.key});
  @override
  State<ExchangeSuccessPage> createState() => _ArticleDetailState();
}

class _ArticleDetailState extends State<ExchangeSuccessPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(S.of(context).exchange),
        centerTitle: true,
        elevation: 0,
        toolbarHeight: 90,
        backgroundColor: Colors.transparent,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        height: double.infinity,
        color: Colors.white,
        child: Column(
          children: [
            bottomPoints(context),
          ],
        ),
      ),
    );
  }

  Widget bottomPoints(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 24),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'images/success.png',
                    width: 240,
                    height: 200,
                  ),
                  Text(
                    S.of(context).exchangeSuccess,
                    style: const TextStyle(fontSize: 32),
                  )
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 80, left: 40, right: 40, bottom: 0),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(18.0),
                        backgroundColor:
                            const Color(0xFF224195), // Background color
                      ),
                      child: Text(
                        S.of(context).backToHome,
                        style: const TextStyle(color: Colors.white),
                      ),
                      onPressed: () async {
                        context.goNamed(RouteNames.home);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
