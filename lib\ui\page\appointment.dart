import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/ui/widgets/input_dart.dart';
import 'package:new_solidcare/view_model/apointment_model.dart';
import 'package:new_solidcare/view_model/base_model.dart';
import 'package:new_solidcare/view_model/base_view.dart';
import '../../generated/l10n.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';

class ApointmentPage extends StatefulWidget {
  const ApointmentPage(
      {super.key, required this.productId, required this.orderId});
  final int productId; //产品ID
  final int orderId; //订单ID
  @override
  State<ApointmentPage> createState() => _ApointmentPageState();
}

class _ApointmentPageState extends State<ApointmentPage> {
  /// 构建服务列表
  _buildServiceWidget(BuildContext context, ApointmentModel model) {
    if (model.state == ViewState.Idle) {
      List<Widget> list = [];
      for (var i = 0; i < model.service.length; i++) {
        String productName = "";
        if (model.service[i]["productNameLangValue"] != null) {
          productName = model.service[i]["productNameLangValue"];
        }
        if (productName.isNotEmpty) {
          list.add(Row(
            children: [
              Checkbox(
                  value: model.service[i]["checked"],
                  onChanged: (value) {
                    model.changeServiceCheck(i, value);
                  }),
              Text(productName)
            ],
          ));
        }
      }
      return Column(
        children: list,
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  /// 构建产品列表
  _buildProductWidget(BuildContext context, ApointmentModel model) {
    if (model.state == ViewState.Idle) {
      List<Widget> list = [];
      for (var i = 0; i < model.product.length; i++) {
        String productName = "";
        if (model.product[i]["productNameLangValue"] != null) {
          productName = model.product[i]["productNameLangValue"];
        }
        if (productName.isNotEmpty) {
          list.add(Row(
            children: [
              Checkbox(
                  value: model.product[i]["checked"],
                  onChanged: (value) {
                    model.changeProductCheck(i, value);
                  }),
              Text(productName)
            ],
          ));
        }
      }
      return Column(
        children: list,
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  /// 显示选日期
  _showDatePicker(BuildContext context, ApointmentModel model) {
    DatePicker.showDatePicker(context,
        minTime: DateTime(2021, 1, 1),
        maxTime: DateTime(2041, 12, 31), onConfirm: (date) {
      model.changeOrderDate(date);
    }, showTitleActions: true);
  }

  /// 显示选日期
  _showTimePicker(BuildContext context, ApointmentModel model) {
    DatePicker.showTimePicker(context, onConfirm: (time) {
      setState(() {
        model.changeOrderTime(time);
      });
    }, showTitleActions: true);
  }

  /// 构建下拉框
  _buildProvince(BuildContext context, ApointmentModel model) {
    if (model.state == ViewState.Idle) {
      List<DropdownMenuItem<String>> items = [];
      for (int i = 0; i < model.province.length; i++) {
        Map item = model.province[i];
        items.add(DropdownMenuItem(
            value: item["titleValue"],
            child: Text(item["titleLangValue"].toString())));
      }
      return DropdownButton(
        isExpanded: true,
        value: model.apointment["province"],
        onChanged: (value) {
          model.changeProvince(value);
        },
        items: items,
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  /// 保存订单
  _saveApointment(BuildContext context, ApointmentModel model) {
    model.saveApointment().then(
      (res) {
        if (200 == res["code"]) {
          Fluttertoast.showToast(
              msg: S.of(context).apointAlert,
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.TOP);
          context.pop(); // This will pop the current route
          context.pop(); // This will pop the previous route
        } else if (305 == res["code"]) {
          //重新登录
          Fluttertoast.showToast(
            msg: S.of(context).loginAlert,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
          );
          context.pushReplacementNamed(RouteNames.login,
              pathParameters: {'type': '0'});
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseView<ApointmentModel>(
      model: ApointmentModel(),
      onModelReady: (model) async {
        model.loadData(widget.productId);
        if (widget.orderId > 0) {
          model.loadApointment(widget.orderId);
        }
      },
      child: widget,
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F5F5),
          appBar: AppBar(
            centerTitle: true,
            elevation: 0,
            toolbarHeight: 90,
            backgroundColor: Colors.transparent,
            title: Text(S.of(context).apointment),
            flexibleSpace: FlexibleSpaceBar(
              background: Image.asset(
                'images/navBg.png', // 你的背景图路径
                fit: BoxFit.cover, // 使图片覆盖整个 AppBar
              ),
            ),
          ),
          body: SingleChildScrollView(
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xFFF5F5F5),
              ),
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  getRow1(model),
                  const Divider(),
                  getRow2(model),
                  getContainer(model),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getRow1(ApointmentModel model) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["firstName"])),
                    onChanged: (v) => {model.apointment["firstName"] = v},
                    hintText: S.of(context).firstName),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["lastName"])),
                    onChanged: (v) => {model.apointment["lastName"] = v},
                    hintText: S.of(context).lastName),
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(5),
                height: 50,
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["address"])),
                    onChanged: (v) => {model.apointment["address"] = v},
                    hintText: S.of(context).address),
              ),
            )
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["unit"])),
                    onChanged: (v) => {model.apointment["unit"] = v},
                    hintText: S.of(context).unit),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: Builder(
                  builder: (context) => _buildProvince(context, model),
                ),
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["city"])),
                    onChanged: (v) => {model.apointment["city"] = v},
                    hintText: S.of(context).city),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["postCode"])),
                    onChanged: (v) => {model.apointment["postCode"] = v},
                    hintText: S.of(context).postCode),
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["phone"])),
                    onChanged: (v) => {model.apointment["phone"] = v},
                    hintText: S.of(context).phone),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["mobile"])),
                    onChanged: (v) => {model.apointment["mobile"] = v},
                    hintText: S.of(context).mobile),
              ),
            ),
          ],
        ),
        Row(
          children: [
            Expanded(
                flex: 1,
                child: Container(
                    padding: const EdgeInsets.all(5),
                    child: RectInput(
                        controller: TextEditingController.fromValue(
                            TextEditingValue(text: model.apointment["email"])),
                        onChanged: (v) => {model.apointment["email"] = v},
                        hintText: S.of(context).email))),
            const Expanded(
              flex: 1,
              child: SizedBox(),
            ),
          ],
        ),
      ],
    );
  }

  Widget getRow2(ApointmentModel model) {
    return Column(
      children: [
        Row(
          children: [
            Text("${S.of(context).apointOrderDate}  "),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    readOnly: true,
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["orderDate"])),
                    onTap: () {
                      _showDatePicker(context, model);
                    },
                    suffixIcon: const Icon(Icons.date_range),
                    hintText: S.of(context).orderDate),
              ),
            )
          ],
        ),
        Row(
          children: [
            Text("${S.of(context).apointOrderTime}  "),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(5),
                child: RectInput(
                    readOnly: true,
                    controller: TextEditingController.fromValue(
                        TextEditingValue(text: model.apointment["orderTime"])),
                    onTap: () {
                      _showTimePicker(context, model);
                    },
                    suffixIcon: const Icon(Icons.access_time),
                    hintText: S.of(context).orderTime),
              ),
            )
          ],
        ),
        Row(
          children: [
            Text(S.of(context).houseYear),
            Container(
              width: 80,
              padding: const EdgeInsets.all(5),
              child: RectInput(
                  controller: TextEditingController.fromValue(TextEditingValue(
                      text: model.apointment["houseYear"].toString())),
                  onChanged: (v) => {model.apointment["houseYear"] = v}),
            ),
            Text(S.of(context).year),
          ],
        ),
        Row(
          children: [
            Text(S.of(context).houseArea),
            Container(
              width: 80,
              padding: const EdgeInsets.all(5),
              child: RectInput(
                  controller: TextEditingController.fromValue(TextEditingValue(
                      text: model.apointment["area"].toString())),
                  onChanged: (v) => {model.apointment["area"] = v}),
            ),
            Text(S.of(context).area),
          ],
        ),
      ],
    );
  }

  Widget getContainer(ApointmentModel model) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          height: 40,
          alignment: const Alignment(-1, -1),
          decoration: BoxDecoration(
            color: const Color(0xff6C84C4),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), topRight: Radius.circular(8.0)),
            border: Border.all(width: 1, color: const Color(0xff6C84C4)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                S.of(context).serviceTitle,
                style: const TextStyle(color: Colors.white),
              )
            ],
          ),
        ),
        Container(
            constraints: const BoxConstraints(minHeight: 50),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8.0),
                  bottomRight: Radius.circular(8.0)),
              border: Border.all(width: 1, color: const Color(0xff6C84C4)),
            ),
            child: Builder(
              builder: (context) => _buildServiceWidget(context, model),
            )),
        const SizedBox(
          height: 20,
        ),
        Container(
          padding: const EdgeInsets.all(6),
          height: 40,
          alignment: const Alignment(-1, -1),
          decoration: BoxDecoration(
            color: const Color(0xff6C84C4),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), topRight: Radius.circular(8.0)),
            border: Border.all(width: 1, color: const Color(0xff6C84C4)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                S.of(context).productTitle,
                style: const TextStyle(color: Colors.white),
              )
            ],
          ),
        ),
        Container(
            constraints: const BoxConstraints(minHeight: 50),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8.0),
                  bottomRight: Radius.circular(8.0)),
              border: Border.all(width: 1, color: const Color(0xff6C84C4)),
            ),
            child: Builder(
              builder: (context) => _buildProductWidget(context, model),
            )),
        const SizedBox(
          height: 20,
        ),
        Container(
          padding: const EdgeInsets.all(6),
          height: 40,
          alignment: const Alignment(-1, -1),
          decoration: BoxDecoration(
            color: const Color(0xff6C84C4),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8.0), topRight: Radius.circular(8.0)),
            border: Border.all(width: 1, color: const Color(0xFF6C84C4)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                S.of(context).remarkTitle,
                style: const TextStyle(color: Colors.white),
              )
            ],
          ),
        ),
        Container(
          constraints: const BoxConstraints(minHeight: 50),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8.0),
                bottomRight: Radius.circular(8.0)),
            border: Border.all(width: 1, color: const Color(0xff6C84C4)),
          ),
          child: RectInput(
              controller: TextEditingController.fromValue(
                  TextEditingValue(text: model.apointment["remark"] ?? "")),
              maxLines: 5,
              onChanged: (v) => {model.apointment["remark"] = v},
              hintText: S.of(context).remarkTitle),
        ),
        Visibility(
          visible: model.apointment["state"] == null ||
              model.apointment["state"] == 0,
          child: Container(
            padding: const EdgeInsets.all(20),
            child: ButtonTheme(
              minWidth: 150.0, //设置最小宽度
              height: 45.0,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(18.0),
                  backgroundColor: const Color(0xFF224195),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0)),
                ),
                child: Text(
                  S.of(context).submit,
                  style: const TextStyle(color: Colors.white),
                ),
                onPressed: () async {
                  _saveApointment(context, model);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
