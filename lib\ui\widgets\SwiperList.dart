import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';

class Swiperlist {
  // 添加一个list<string>来存放图片的路径
  final List<String> imgList;
  final double height;
  Swiperlist(this.imgList, this.height);

  Container getSwiper(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height * 0.4,
        child: Swiper.children(
          autoplay: true,
          pagination: const SwiperPagination(
            margin: EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
            builder: DotSwiperPaginationBuilder(
                color: Colors.white30,
                activeColor: Colors.white,
                size: 10.0,
                activeSize: 10.0),
          ),
          children: imgList.map(
            (path) {
              return AspectRatio(
                aspectRatio: 4 / 3,
                child: Image.network(
                  path,
                  fit: BoxFit.cover,
                ),
              );
            },
          ).toList(),
        ),
      ),
    );
  }
}
