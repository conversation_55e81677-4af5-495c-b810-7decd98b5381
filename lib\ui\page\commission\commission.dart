import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/ui/widgets/SwiperList.dart';
import 'package:new_solidcare/utils/global.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../generated/l10n.dart';
import 'package:new_solidcare/utils/shared_preferences_util.dart';

/// 佣金页面
class Commission extends StatefulWidget {
  const Commission({super.key});
  @override
  State<Commission> createState() => _CommissionState();
}

class _CommissionState extends State<Commission> {
  @override
  void initState() {
    super.initState();
    showCurUser();
  }

  ImageProvider curHeadImg = const AssetImage('images/login-out.png');
  String articleTitle = "";
  String curUserName = S.current.unlogin;
  int userId = 0;
  int interal = 0;
  int useInteral = 0;
  bool isLogin = false;

  List<String> imgList = [];

  showCurUser() {
    SharedPreferencesUtil.getInstance().getString("User").then((value) {
      setState(() {
        if (value != null) {
          var user = json.decode(value);
          curUserName = user["customerName"];
          userId = user["id"];
          curHeadImg = const AssetImage('images/login-in.png');
          isLogin = true;
          Global.getIntergral().then((data) {
            setState(() {
              interal = data?.interal ?? 0;
              useInteral = data?.useInteral ?? 0;
            });
          });
        } else {
          isLogin = false;
        }
      });
    });
  }

  Future loadSwiper() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Article";
    parameters["params"] = {"type": "promotion pic."};
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  //构造swiper
  Widget _buildFuture(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: Text(S.of(context).earnCommission),
        toolbarHeight: 90,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              _buildUserHeader(context),
              _buildPointsInfo(),
              _buildDivider(),
              _buildActionButtons(context),
              FutureBuilder(future: loadSwiper(), builder: _buildFutureSwiper),
              _buildBottomList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
        child: Divider(height: 1, color: Colors.grey[300]!));
  }

  Widget _buildFutureSwiper(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasError) {
      return Center(child: Text('Error: ${snapshot.error}'));
    }
    if (snapshot.connectionState == ConnectionState.done) {
      var data = snapshot.data;
      if (data["code"] == 200) {
        var list = data["data"]["list"];
        if (list.length > 0) {
          imgList = [];
          for (int i = 0; i < list.length; i++) {
            var picUrl = list[i]["picUrl"];
            imgList.add(picUrl);
          }
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: Swiperlist(imgList, 135).getSwiper(context),
          );
        }
      }
    }
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildUserHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: Colors.white,
            backgroundImage: curHeadImg,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isLogin ? curUserName : S.of(context).unlogin,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const Spacer(),
          Text(
            "ID:$userId",
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "${S.of(context).exchangeablePoints}: ",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              Transform.translate(
                offset: const Offset(0, 5),
                child: Text(
                  interal.toString(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  "${S.of(context).totalPoints}: ",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Transform.translate(
                  offset: const Offset(0, 5),
                  child: Text(
                    useInteral.toString(),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildActionButton("images/gift.png", S.of(context).giftExchange,
              onTap: () {
            context.pushNamed(RouteNames.exchangemall);
          }),
          _buildActionButton("images/glod.png", S.of(context).withdrawal,
              onTap: () {
            context.pushNamed(RouteNames.withdrawcash);
          }),
          _buildActionButton("images/list.png", S.of(context).pointsDetails,
              onTap: () {
            context.pushNamed(RouteNames.pointsDetails);
          }),
          _buildActionButton("images/shared.png", S.of(context).shareEarnPoints,
              onTap: () {
            context.pushNamed(RouteNames.shared);
          }),
        ],
      ),
    );
  }

  Widget _buildActionButton(String src, String label, {Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Image.asset(
            src,
            width: 48,
            height: 48,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomList() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildListItem(S.of(context).rewardRules, onTap: () {
            context.pushNamed(RouteNames.awardrule);
          }),
          _buildListItem(S.of(context).withdrawalRecord, onTap: () {
            context.pushNamed(RouteNames.reminderRecord);
          }),
          _buildListItem(S.of(context).exchangeRecord, onTap: () {
            context.pushNamed(RouteNames.redemptionHistory);
          }),
          _buildListItem(S.of(context).friendsList, onTap: () {
            context.pushNamed(RouteNames.friends);
          }),
          _buildListItem(S.of(context).customerService, onTap: () {
            launchUrl(Uri(scheme: 'tel', path: "************"));
          }),
        ],
      ),
    );
  }

  Widget _buildListItem(String title, {Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Text(title),
            const Spacer(),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return _buildFuture(context);
  }
}
