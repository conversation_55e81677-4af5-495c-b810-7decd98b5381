import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/ui/widgets/HexImageWidget.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:universal_html/html.dart' as html;

/// 分享海报页面
class SharedPage extends StatefulWidget {
  const SharedPage({super.key});

  @override
  State<SharedPage> createState() => _SharedPageState();
}

class _SharedPageState extends State<SharedPage> {
  String sharedImg = "";

  late Future<Map<String, dynamic>> _pageDataFuture; // 声明 Future 变量
  bool _mounted = true; // 添加标记
  @override
  void initState() {
    super.initState();
    _loadData(); // 在 initState 中初始化
  }

  @override
  void dispose() {
    _mounted = false; // 标记组件已销毁
    super.dispose();
  }

  Future<void> _saveImage() async {
    if (sharedImg.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).noImageToSave)),
      );
      return;
    }

    try {
      if (kIsWeb) {
        await _saveImageWeb(sharedImg); // 传入 sharedImg 参数
      } else {
        await _saveImageMobile(sharedImg); // 传入 sharedImg 参数
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${S.of(context).saveFailed}: $e')),
      );
    }
  }

  Future<void> _saveImageWeb(String imageData) async {
    try {
      // 确保我们有正确的 base64 数据
      String base64Data = imageData;

      // 如果不是完整的 Data URL，添加前缀
      if (!imageData.contains('data:image')) {
        base64Data = 'data:image/jpeg;base64,' + imageData;
      }

      // 创建 Blob
      final bytes = base64Decode(base64Data.split(',').last);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);

      // 创建下载链接并触发下载
      final anchor = html.AnchorElement()
        ..href = url
        ..setAttribute('download',
            'shared_image_${DateTime.now().millisecondsSinceEpoch}.jpg')
        ..click();

      // 清理
      html.Url.revokeObjectUrl(url);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).downloadStarted)),
      );
    } catch (e) {
      // 如果直接下载失败，尝试在新窗口中打开
      try {
        final newWindow = html.window.open('', '_blank');
        if (newWindow != null) {
          newWindow.location.href = imageData;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(S.of(context).saveInNewWindow)),
          );
        } else {
          throw Exception(S.of(context).cannotOpenNewWindow);
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${S.of(context).saveFailed}: $e')),
        );
      }
    }
  }

  Future<void> _saveImageMobile(String imageData) async {
    // 请求权限
    if (!await _requestPermission(context)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).needStoragePermission)),
      );
      return;
    }

    // 处理 Data URL 格式
    String base64Data = imageData;
    if (imageData.contains('data:image')) {
      base64Data = imageData.split(',')[1];
    }

    final Uint8List bytes = base64Decode(base64Data);
    final result = await ImageGallerySaverPlus.saveImage(
      bytes,
      quality: 100,
      name: 'shared_image_${DateTime.now().millisecondsSinceEpoch}',
    );

    if (result['isSuccess']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).imageSavedToGallery)),
      );
    } else {
      throw Exception(S.of(context).needStoragePermission);
    }
  }

  Future<bool> _requestPermission(BuildContext context) async {
    var status = await Permission.storage.status;
    if (status.isGranted) {
      return true;
    } else {
      var result = await Permission.storage.request();
      if (result.isGranted) {
        return true;
      } else {
        // 提示用户手动开启权限
        bool shouldOpenSettings = await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(S.of(context).permissionReject),
            content: Text(S.of(context).permissionDesc),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(S.of(context).permissionClear),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(S.of(context).permissionSetting),
              ),
            ],
          ),
        );
        if (shouldOpenSettings) {
          await openAppSettings(); // 跳转到应用设置页面
        }
        return false;
      }
    }
  }

  Future<void> _loadData() async {
    if (!_mounted) return;
    _pageDataFuture = loadPageData();
  }

  Future<Map<String, dynamic>> loadPageData() async {
    if (!_mounted) return {};

    try {
      final results = await HttpUtil.instance.post(
        "/v1/customer/shareToEarnCommissions",
      );

      // 检查组件是否还在树中
      if (!_mounted) return {};

      if (results != null && results['code'] == 200) {
        sharedImg = results['data'] ?? '';
      }
      return results ?? {};
    } catch (e) {
      debugPrint('Error loading page data: $e');
      return {};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).awardRule),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: FutureBuilder<Map<String, dynamic>>(
          future: _pageDataFuture,
          builder: (context, snapshot) {
            if (!_mounted) return const SizedBox.shrink();

            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }
            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }

            return Stack(
              children: [
                Column(
                  children: [
                    _buildTopInfo(context),
                    const SizedBox(height: 4),
                    _buildSaveImg(context),
                    const SizedBox(height: 4),
                    _buildImgInfo(snapshot),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildTopInfo(BuildContext context) {
    String txt1 = S.of(context).promotionTitle;
    String txt2 = S.of(context).promotionDesc1;
    String txt3 = S.of(context).promotionDesc2;
    String txt4 = S.of(context).promotionDesc3;
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      child: Column(
        children: [
          SizedBox(
            height: 100,
            child: Text(
              "$txt1$txt2$txt3$txt4",
              style: const TextStyle(
                color: Color(0xFFFF9800),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveImg(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        InkWell(
          onTap: _saveImage,
          borderRadius: BorderRadius.circular(30),
          child: Container(
            width: 80,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                bottomLeft: Radius.circular(20),
                topRight: Radius.circular(0),
                bottomRight: Radius.circular(0),
              ),
            ),
            child: Center(
              child: Text(
                S.of(context).saveImage,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildImgInfo(AsyncSnapshot snapshot) {
    sharedImg = snapshot.data?['data']; // 安全地获取数据
    return Expanded(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 0),
        child: sharedImg.isNotEmpty
            ? HexImageWidget(base64String: sharedImg)
            : const Center(child: Icon(Icons.image_not_supported)),
      ),
    );
  }
}
