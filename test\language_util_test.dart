import 'package:flutter_test/flutter_test.dart';
import 'package:new_solidcare/utils/language_util.dart';

void main() {
  group('LanguageUtil Tests', () {
    test('getSystemLanguage should return supported language codes', () {
      // 测试系统语言映射
      expect(LanguageUtil.supportedLanguages.contains('en'), true);
      expect(LanguageUtil.supportedLanguages.contains('zh_CN'), true);
      expect(LanguageUtil.supportedLanguages.contains('zh_HK'), true);
    });

    test('getLanguageDisplayName should return correct display names', () {
      expect(LanguageUtil.getLanguageDisplayName('en'), 'English');
      expect(LanguageUtil.getLanguageDisplayName('zh_CN'), '中文简体');
      expect(LanguageUtil.getLanguageDisplayName('zh_HK'), '中文繁体');
      expect(LanguageUtil.getLanguageDisplayName('unknown'), 'English');
    });

    test('getLanguageIndex should return correct indices', () {
      expect(LanguageUtil.getLanguageIndex('en'), 0);
      expect(LanguageUtil.getLanguageIndex('zh_CN'), 1);
      expect(LanguageUtil.getLanguageIndex('zh_HK'), 2);
      expect(LanguageUtil.getLanguageIndex('unknown'), 0);
    });

    test('getLanguageCodeByIndex should return correct language codes', () {
      expect(LanguageUtil.getLanguageCodeByIndex(0), 'en');
      expect(LanguageUtil.getLanguageCodeByIndex(1), 'zh_CN');
      expect(LanguageUtil.getLanguageCodeByIndex(2), 'zh_HK');
      expect(LanguageUtil.getLanguageCodeByIndex(99), 'en');
    });
  });
}
