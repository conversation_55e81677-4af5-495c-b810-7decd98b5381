import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../generated/l10n.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../utils/http_util.dart';

class ContactPage extends StatefulWidget {
  const ContactPage({super.key});

  @override
  State<ContactPage> createState() => _ContactPageState();
}

class _ContactPageState extends State<ContactPage> {
  String customerName = "";
  String address = "";
  String email = "";
  String phone = "";
  String content = "";

  @override
  void initState() {
    super.initState();
  }

  submitContact() {
    if (customerName == "" || email == "") {
      Fluttertoast.showToast(
          msg: S.of(context).contactAlert,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP);
    }
    Map<String, dynamic> parameters = {};
    parameters["customerName"] = customerName;
    parameters["address"] = address;
    parameters["email"] = email;
    parameters["phone"] = phone;
    parameters["content"] = content;
    HttpUtil.instance
        .post("/v1/guestbook/save", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        //登录成功
        Fluttertoast.showToast(
            msg: S.of(context).contactAlert2,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP);
        context.pop();
      } else {
        Fluttertoast.showToast(
            msg: res["msg"],
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).contact),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(
            margin:
                const EdgeInsets.only(left: 10, right: 10, top: 40, bottom: 20),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: const BorderRadius.all(Radius.circular(8.0)),
              border: Border.all(width: 1, color: Colors.grey),
            ),
            child: Column(
              children: [
                const Row(
                  children: [
                    Text("Name"),
                    Text("*"),
                    SizedBox(
                      height: 40,
                    )
                  ],
                ),
                TextField(
                  onChanged: (v) {
                    setState(() {
                      customerName = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
                const Row(
                  children: [
                    Text("Address"),
                    SizedBox(
                      height: 40,
                    )
                  ],
                ),
                TextField(
                  onChanged: (v) {
                    setState(() {
                      address = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
                const Row(
                  children: [
                    Text("E-mail ID"),
                    Text("*"),
                    SizedBox(
                      height: 40,
                    )
                  ],
                ),
                TextField(
                  onChanged: (v) {
                    setState(() {
                      email = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
                const Row(
                  children: [
                    Text("Phone"),
                    SizedBox(
                      height: 40,
                    )
                  ],
                ),
                TextField(
                  onChanged: (v) {
                    setState(() {
                      phone = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
                const Row(
                  children: [
                    Text("What are you looking for?"),
                    SizedBox(
                      height: 40,
                    )
                  ],
                ),
                TextField(
                  maxLines: 5,
                  onChanged: (v) {
                    setState(() {
                      content = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: const InputDecoration(
                      isCollapsed: true,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
                const SizedBox(
                  height: 20,
                ),
                Container(
                    padding: const EdgeInsets.all(10),
                    child: SizedBox(
                        width: double.infinity, // 宽度无限，跟父控件保持一致
                        child: ButtonTheme(
                            height: 50.0,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(15.0),
                                backgroundColor:
                                    const Color(0xFF224195), // Background color
                              ),
                              child: Text(
                                S.of(context).submit,
                                style: const TextStyle(color: Colors.white),
                              ),
                              onPressed: () async {
                                submitContact();
                              },
                            ))))
              ],
            )),
      ),
    );
  }
}
