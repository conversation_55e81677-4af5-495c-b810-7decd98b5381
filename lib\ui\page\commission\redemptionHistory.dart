import 'package:flutter/material.dart';
import 'package:new_solidcare/constant/app_colors.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:intl/intl.dart';

/// 兑换记录页面
class RedemptionHistory extends StatefulWidget {
  const RedemptionHistory({super.key});

  @override
  State<RedemptionHistory> createState() => _RedemptionHistoryState();
}

class _RedemptionHistoryState extends State<RedemptionHistory>
    with SingleTickerProviderStateMixin {
  String lang = "en";
  int pageIndex = 1;
  bool isLoading = false;
  bool hasMore = true;
  List recordList = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    var localLang = Intl.getCurrentLocale();
    setState(() {
      if (localLang == "zh_CN") {
        lang = "zh";
      } else if (localLang == "zh_HK") {
        lang = "es";
      }
    });

    // 添加滚动监听
    _scrollController.addListener(_onScroll);
    // 初始加载数据
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    if (currentScroll >= (maxScroll - 200) && !isLoading && hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
      pageIndex++;
    });

    await _loadData();
  }

  Future<void> _loadData() async {
    try {
      Map<String, dynamic> parameters = {
        "query": "OrderList",
        "page": pageIndex,
        "limit": 10,
      };

      final result = await HttpUtil.instance
          .post("/v1/exchange/order/query", parameters: parameters);

      if (result != null && result["code"] == 200) {
        List newList = result["data"]["list"] ?? [];
        int total = result["data"]["total"] ?? 0;

        setState(() {
          if (pageIndex == 1) {
            recordList = newList;
          } else {
            recordList.addAll(newList);
          }

          hasMore = recordList.length < total;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).exchangeRecord),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            setState(() {
              pageIndex = 1;
              hasMore = true;
            });
            await _loadData();
          },
          child: ListView(
            controller: _scrollController,
            children: [
              getPointsList(recordList),
              if (isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
              if (!hasMore && recordList.isNotEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(S.of(context).noMoreData),
                  ),
                ),
              // 添加底部空间，确保最后一项可以完全滚动到顶部
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget getPointsList(List pointsData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      child: Column(
        children: pointsData.asMap().entries.map((entry) {
          String imageUrl = "https://fakeimg.pl/120x90";
          // 根据颜色选择字体颜色
          int index = entry.key; // 获取索引
          var point = entry.value; // 获取当前元素
          if (lang == "en") {
            imageUrl = point['photoEn'] ?? imageUrl;
          } else if (lang == "zh") {
            imageUrl = point['photoZh'] ?? imageUrl;
          } else if (lang == "es") {
            imageUrl = point['photoEs'] ?? imageUrl;
          }
          //  point['state'] = 0 申请中 1 已审核 2 已发货 3 作废
          var state = point['state']!;
          if (state == 0) {
            state = S.of(context).applying;
          } else if (state == 1) {
            state = S.of(context).reviewed;
          } else if (state == 2) {
            state = S.of(context).shipped;
          } else if (state == 3) {
            state = S.of(context).cancelled;
          }
          // point['subTime']! 截取 yyyy-MM-dd HH:mm:ss 时间
          // 提取提交时间
          String submitTime = point['subTime']!;
          String formattedSubmitTime =
              DateFormat('yyyy-MM-dd').format(DateTime.parse(submitTime));

          String paymentTime = point['updatedTime']!;
          String formattedPaymentTime =
              DateFormat('yyyy-MM-dd').format(DateTime.parse(paymentTime));

          Color borderColor = index == pointsData.length - 1
              ? const Color.fromARGB(255, 255, 255, 255)
              : const Color(0xFFE5E5E5);

          return Container(
            margin: const EdgeInsets.only(bottom: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: borderColor,
                  width: 1.0,
                ),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      point['productName']!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          getItem(S.of(context).submitTime, formattedSubmitTime,
                              color: Colors.grey[600]),
                          const SizedBox(height: 6),
                          getItem(
                              S.of(context).paymentTime, formattedPaymentTime,
                              color: Colors.grey[600]),
                          const SizedBox(height: 6),
                          const SizedBox(height: 6),
                          getItem(S.of(context).quantity, point['qty']!,
                              color: Colors.grey[600]),
                          const SizedBox(height: 6),
                          getItem(S.of(context).status, state,
                              color: Colors.grey[600]),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 130,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: Image.network(
                              imageUrl,
                              width: 120,
                              height: 90,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(height: 6),
                          SizedBox(
                            width: 120, // 与图片等宽
                            child: Text(
                              "${S.of(context).points}${point['integral']!}",
                              style: const TextStyle(
                                fontSize: 14.0,
                                color: AppColors.COLOR_PRIMARY,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget getItem(name, title,
      {color = Colors.black,
      fonsSIze = 14.0,
      FontWeight fontWeight = FontWeight.normal}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              name,
              style: TextStyle(
                color: color,
                fontSize: fonsSIze,
                fontWeight: fontWeight,
              ),
            ),
          ),
        ),
        SizedBox(
          width: 100,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '$title',
              style: TextStyle(
                color: color,
                fontSize: fonsSIze,
                fontWeight: fontWeight,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
