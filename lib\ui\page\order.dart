import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import '../../generated/l10n.dart';
import '../../utils/http_util.dart';

class OrderPage extends StatefulWidget {
  _OrderPageState pageState = _OrderPageState();

  OrderPage({super.key});
  @override
  State<OrderPage> createState() => pageState;

  reload() {
    pageState.reload();
  }
}

class _OrderPageState extends State<OrderPage> {
  List orderList = [];
  String title = "";

  reload() {
    setState(() {
      title = "";
    });
  }

  //获取服务列表
  Future loadOrder() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "MobileList";
    return HttpUtil.instance.post("/v1/order/query", parameters: parameters);
  }

  gotoLogin(BuildContext content, actionType) {
    context.pushNamed(RouteNames.login, pathParameters: {'type': '0'});
    reload();
  }

  Widget getOrderState(int state) {
    String stateDesc = S.of(context).orderState4;
    if (0 == state) {
      stateDesc = S.of(context).orderState0;
    } else if (1 == state) {
      stateDesc = S.of(context).orderState1;
    } else if (2 == state) {
      stateDesc = S.of(context).orderState2;
    } else if (3 == state) {
      stateDesc = S.of(context).orderState3;
    }
    return Text(
      stateDesc,
      style: const TextStyle(color: Color(0xFF224195)),
    );
  }

  //构造swiper
  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      if (snapshot.data["code"] == 200) {
        return showOrderList(context, snapshot);
      } else {
        //未登录
        return showLogin(context);
      }
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  Column showLogin(BuildContext context) {
    //未登录
    title = S.of(context).selfTitle;
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(30),
          child: const Center(
            child: Image(width: 200, image: AssetImage("images/logo.png")),
          ),
        ),
        Container(
          padding:
              const EdgeInsets.only(left: 0, top: 30, right: 0, bottom: 30),
          child: Center(
            child: Text(
              S.of(context).loginTitle,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        Container(
          padding:
              const EdgeInsets.only(left: 50, top: 30, right: 50, bottom: 5),
          child: Row(
            children: <Widget>[
              Expanded(
                child: ButtonTheme(
                  height: 60.0,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(18.0),
                      backgroundColor:
                          const Color(0xFF224195), // Background color
                    ),
                    child: Text(S.of(context).login,
                        style:
                            const TextStyle(fontSize: 16, color: Colors.white)),
                    onPressed: () {
                      gotoLogin(context, 0);
                    },
                  ),
                ),
              )
            ],
          ),
        ),
        Container(
          padding:
              const EdgeInsets.only(left: 50, top: 5, right: 50, bottom: 5),
          child: Row(
            children: <Widget>[
              Expanded(
                child: ButtonTheme(
                  height: 60.0,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(18.0),
                      backgroundColor:
                          const Color(0xFF9C9C9C), // Background color
                    ),
                    child: Text(
                      S.of(context).register,
                      style: const TextStyle(fontSize: 16, color: Colors.white),
                    ),
                    onPressed: () {
                      gotoLogin(context, 1);
                    },
                  ),
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  ListView showOrderList(
      BuildContext context, AsyncSnapshot<dynamic> snapshot) {
    title = S.of(context).orderTitle;
    orderList = snapshot.data["data"]["list"];
    return ListView.builder(
        itemCount: orderList.length,
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
            onTap: () {
              context.pushNamed(RouteNames.apointment, pathParameters: {
                'productId': '0',
                'orderId': orderList[index]["id"].toString()
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: index % 2 == 0 ? const Color(0xFFF5F5F5) : Colors.white,
                border: const Border(
                  bottom: BorderSide(width: 1, color: Color(0xFF9C9C9C)),
                ),
              ),
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 5,
                        child: Text(
                          orderList[index]["productName"],
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [getOrderState(orderList[index]["state"])],
                        ),
                      )
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        orderList[index]["orderDate"],
                        style: const TextStyle(fontSize: 16),
                      )
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        title: Text(title),
        toolbarHeight: 90,
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: FutureBuilder(future: loadOrder(), builder: _buildFuture),
    );
  }
}
