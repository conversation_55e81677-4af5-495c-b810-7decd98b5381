import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/http_util.dart';

///兑换礼品页面
class Redeem extends StatefulWidget {
  final int mallId;

  const Redeem({super.key, required this.mallId});

  @override
  State<Redeem> createState() => _RedeemState();
}

class _RedeemState extends State<Redeem> {
  //获取服务列表
  Future loadAwardRule() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "ProductDetail";
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).awardRule),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
          decoration: const BoxDecoration(
            color: Color(0xfff5f5f5),
          ),
          child: FutureBuilder(future: loadAwardRule(), builder: _buildFuture)),
    );
  }

  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      String html = "";
      if (snapshot.data["code"] == 200) {
        List list = snapshot.data["data"]["list"];
        if (list.isNotEmpty) {
          html = list[0]["description"];
        }
      }
      return Column(
        children: [
          Expanded(child: SingleChildScrollView(child: Html(data: html))),
          Padding(
              padding: const EdgeInsets.only(top: 28.0),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(18.0),
                        backgroundColor:
                            const Color(0xFF224195), // Background color
                      ),
                      child: Text(
                        S.of(context).goApointment,
                        style: const TextStyle(color: Colors.white),
                      ),
                      onPressed: () async {
                        context.go(RoutePaths.home);
                      },
                    ),
                  ),
                ],
              )),
        ],
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }
}
