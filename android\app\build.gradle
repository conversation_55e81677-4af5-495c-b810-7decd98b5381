plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

dependencies {
    implementation "com.wonderpush:wonderpush-android-sdk:3.+"
    // WonderPush SDK core module
    implementation 'com.wonderpush:wonderpush-android-sdk:4.+'
    // WonderPush compatibility module with Firebase Cloud Messaging
    implementation 'com.wonderpush:wonderpush-android-sdk-fcm:1.+'
}

android {
    namespace = "com.example.new_solidcare"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.weitiao.new_solidcare"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = "3"
        versionName = "1.0.3"

        // buildConfigField 'String', 'WONDERPUSH_CLIENT_ID', '58ece48f3cc119563dffc2477e55d834a2b90d2c'
        // buildConfigField 'String', 'WONDERPUSH_CLIENT_SECRET', 'e874730d4ab79a9712e7c7a57ae4232306031eb3b6de5bf3d100d77f24fee053'
        // buildConfigField 'String', 'WONDERPUSH_SENDER_ID', '839965742921'
    }

    signingConfigs {
        debug {
            storeFile file('../key.jks')
            storePassword 'weitiao@3711'
            keyAlias = 'key'
            keyPassword 'weitiao@3711'
        }
        release {
            storeFile file('../key.jks')
            storePassword 'weitiao@3711'
            keyAlias = 'key'
            keyPassword 'weitiao@3711'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}