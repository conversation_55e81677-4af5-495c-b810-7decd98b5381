// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Category`
  String get category {
    return Intl.message(
      'Category',
      name: 'category',
      desc: '',
      args: [],
    );
  }

  /// `Service`
  String get service {
    return Intl.message(
      'Service',
      name: 'service',
      desc: '',
      args: [],
    );
  }

  /// `Order`
  String get order {
    return Intl.message(
      'Order',
      name: 'order',
      desc: '',
      args: [],
    );
  }

  /// `Personal`
  String get personal {
    return Intl.message(
      'Personal',
      name: 'personal',
      desc: '',
      args: [],
    );
  }

  /// `Loading`
  String get loading {
    return Intl.message(
      'Loading',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `About`
  String get about {
    return Intl.message(
      'About',
      name: 'about',
      desc: '',
      args: [],
    );
  }

  /// `Service Area`
  String get servicearea {
    return Intl.message(
      'Service Area',
      name: 'servicearea',
      desc: '',
      args: [],
    );
  }

  /// `Blog`
  String get blok {
    return Intl.message(
      'Blog',
      name: 'blok',
      desc: '',
      args: [],
    );
  }

  /// `Contact Us`
  String get contact {
    return Intl.message(
      'Contact Us',
      name: 'contact',
      desc: '',
      args: [],
    );
  }

  /// `Contact Info`
  String get contactTye {
    return Intl.message(
      'Contact Info',
      name: 'contactTye',
      desc: '',
      args: [],
    );
  }

  /// `Appointment`
  String get apointment {
    return Intl.message(
      'Appointment',
      name: 'apointment',
      desc: '',
      args: [],
    );
  }

  /// `Go Apointment`
  String get goApointment {
    return Intl.message(
      'Go Apointment',
      name: 'goApointment',
      desc: '',
      args: [],
    );
  }

  /// `First name`
  String get firstName {
    return Intl.message(
      'First name',
      name: 'firstName',
      desc: '',
      args: [],
    );
  }

  /// `Last name`
  String get lastName {
    return Intl.message(
      'Last name',
      name: 'lastName',
      desc: '',
      args: [],
    );
  }

  /// `Address`
  String get address {
    return Intl.message(
      'Address',
      name: 'address',
      desc: '',
      args: [],
    );
  }

  /// `Unit`
  String get unit {
    return Intl.message(
      'Unit',
      name: 'unit',
      desc: '',
      args: [],
    );
  }

  /// `Province`
  String get province {
    return Intl.message(
      'Province',
      name: 'province',
      desc: '',
      args: [],
    );
  }

  /// `City`
  String get city {
    return Intl.message(
      'City',
      name: 'city',
      desc: '',
      args: [],
    );
  }

  /// `Post code`
  String get postCode {
    return Intl.message(
      'Post code',
      name: 'postCode',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get phone {
    return Intl.message(
      'Phone',
      name: 'phone',
      desc: '',
      args: [],
    );
  }

  /// `Mobile`
  String get mobile {
    return Intl.message(
      'Mobile',
      name: 'mobile',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message(
      'Email',
      name: 'email',
      desc: '',
      args: [],
    );
  }

  /// `Order date`
  String get orderDate {
    return Intl.message(
      'Order date',
      name: 'orderDate',
      desc: '',
      args: [],
    );
  }

  /// `Order time`
  String get orderTime {
    return Intl.message(
      'Order time',
      name: 'orderTime',
      desc: '',
      args: [],
    );
  }

  /// `Year of house`
  String get houseYear {
    return Intl.message(
      'Year of house',
      name: 'houseYear',
      desc: '',
      args: [],
    );
  }

  /// `Area of house`
  String get houseArea {
    return Intl.message(
      'Area of house',
      name: 'houseArea',
      desc: '',
      args: [],
    );
  }

  /// `Year`
  String get year {
    return Intl.message(
      'Year',
      name: 'year',
      desc: '',
      args: [],
    );
  }

  /// `Square foot`
  String get area {
    return Intl.message(
      'Square foot',
      name: 'area',
      desc: '',
      args: [],
    );
  }

  /// `Service options`
  String get serviceTitle {
    return Intl.message(
      'Service options',
      name: 'serviceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Product options`
  String get productTitle {
    return Intl.message(
      'Product options',
      name: 'productTitle',
      desc: '',
      args: [],
    );
  }

  /// `Fill situation of house`
  String get remarkTitle {
    return Intl.message(
      'Fill situation of house',
      name: 'remarkTitle',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Please login first`
  String get loginAlert {
    return Intl.message(
      'Please login first',
      name: 'loginAlert',
      desc: '',
      args: [],
    );
  }

  /// `Apointment success`
  String get apointAlert {
    return Intl.message(
      'Apointment success',
      name: 'apointAlert',
      desc: '',
      args: [],
    );
  }

  /// `Sign in for the best experience`
  String get loginTitle {
    return Intl.message(
      'Sign in for the best experience',
      name: 'loginTitle',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message(
      'Register',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Not login`
  String get unlogin {
    return Intl.message(
      'Not login',
      name: 'unlogin',
      desc: '',
      args: [],
    );
  }

  /// `Order list`
  String get orderTitle {
    return Intl.message(
      'Order list',
      name: 'orderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Member center`
  String get selfTitle {
    return Intl.message(
      'Member center',
      name: 'selfTitle',
      desc: '',
      args: [],
    );
  }

  /// `Apointment time`
  String get apointTime {
    return Intl.message(
      'Apointment time',
      name: 'apointTime',
      desc: '',
      args: [],
    );
  }

  /// `Preferential`
  String get preferential {
    return Intl.message(
      'Preferential',
      name: 'preferential',
      desc: '',
      args: [],
    );
  }

  /// `How to get more benefits?`
  String get morepreferential {
    return Intl.message(
      'How to get more benefits?',
      name: 'morepreferential',
      desc: '',
      args: [],
    );
  }

  /// `Select language`
  String get selectLang {
    return Intl.message(
      'Select language',
      name: 'selectLang',
      desc: '',
      args: [],
    );
  }

  /// `Create account.New to solidcare?`
  String get regTitle {
    return Intl.message(
      'Create account.New to solidcare?',
      name: 'regTitle',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get loginName {
    return Intl.message(
      'Name',
      name: 'loginName',
      desc: '',
      args: [],
    );
  }

  /// `email`
  String get loginMobile {
    return Intl.message(
      'email',
      name: 'loginMobile',
      desc: '',
      args: [],
    );
  }

  /// `Create password`
  String get regPasword {
    return Intl.message(
      'Create password',
      name: 'regPasword',
      desc: '',
      args: [],
    );
  }

  /// `Invitation code`
  String get regCode {
    return Intl.message(
      'Invitation code',
      name: 'regCode',
      desc: '',
      args: [],
    );
  }

  /// `Show password`
  String get showPassword {
    return Intl.message(
      'Show password',
      name: 'showPassword',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continute {
    return Intl.message(
      'Continue',
      name: 'continute',
      desc: '',
      args: [],
    );
  }

  /// `By creating an account, you agree to silidcare’s Conditions of Use and Privacy Notice`
  String get regContent {
    return Intl.message(
      'By creating an account, you agree to silidcare’s Conditions of Use and Privacy Notice',
      name: 'regContent',
      desc: '',
      args: [],
    );
  }

  /// `Sign-In.Already a customer?`
  String get loginTitle2 {
    return Intl.message(
      'Sign-In.Already a customer?',
      name: 'loginTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get loginPassword {
    return Intl.message(
      'Password',
      name: 'loginPassword',
      desc: '',
      args: [],
    );
  }

  /// `Please input login name and password`
  String get loginAlert2 {
    return Intl.message(
      'Please input login name and password',
      name: 'loginAlert2',
      desc: '',
      args: [],
    );
  }

  /// `Order date`
  String get apointOrderDate {
    return Intl.message(
      'Order date',
      name: 'apointOrderDate',
      desc: '',
      args: [],
    );
  }

  /// `Order time`
  String get apointOrderTime {
    return Intl.message(
      'Order time',
      name: 'apointOrderTime',
      desc: '',
      args: [],
    );
  }

  /// `Uncommitted`
  String get orderState0 {
    return Intl.message(
      'Uncommitted',
      name: 'orderState0',
      desc: '',
      args: [],
    );
  }

  /// `Submitted`
  String get orderState1 {
    return Intl.message(
      'Submitted',
      name: 'orderState1',
      desc: '',
      args: [],
    );
  }

  /// `Finish`
  String get orderState2 {
    return Intl.message(
      'Finish',
      name: 'orderState2',
      desc: '',
      args: [],
    );
  }

  /// `Canceled`
  String get orderState3 {
    return Intl.message(
      'Canceled',
      name: 'orderState3',
      desc: '',
      args: [],
    );
  }

  /// `Confirmed`
  String get orderState4 {
    return Intl.message(
      'Confirmed',
      name: 'orderState4',
      desc: '',
      args: [],
    );
  }

  /// `Know more`
  String get leanmore {
    return Intl.message(
      'Know more',
      name: 'leanmore',
      desc: '',
      args: [],
    );
  }

  /// `Share invitation`
  String get shareLink {
    return Intl.message(
      'Share invitation',
      name: 'shareLink',
      desc: '',
      args: [],
    );
  }

  /// `Please enter mailbox`
  String get sharePlaceHolder {
    return Intl.message(
      'Please enter mailbox',
      name: 'sharePlaceHolder',
      desc: '',
      args: [],
    );
  }

  /// `Change password`
  String get changePwd {
    return Intl.message(
      'Change password',
      name: 'changePwd',
      desc: '',
      args: [],
    );
  }

  /// `Please enter new password`
  String get changePwdHolder {
    return Intl.message(
      'Please enter new password',
      name: 'changePwdHolder',
      desc: '',
      args: [],
    );
  }

  /// `Invitation sent`
  String get shareSuccess {
    return Intl.message(
      'Invitation sent',
      name: 'shareSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Register success`
  String get regSuccess {
    return Intl.message(
      'Register success',
      name: 'regSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Please input name and email`
  String get contactAlert {
    return Intl.message(
      'Please input name and email',
      name: 'contactAlert',
      desc: '',
      args: [],
    );
  }

  /// `Submit success`
  String get contactAlert2 {
    return Intl.message(
      'Submit success',
      name: 'contactAlert2',
      desc: '',
      args: [],
    );
  }

  /// `Please input email`
  String get contactAlert3 {
    return Intl.message(
      'Please input email',
      name: 'contactAlert3',
      desc: '',
      args: [],
    );
  }

  /// `Your password has reset,Please look your mailbox`
  String get contactAlert4 {
    return Intl.message(
      'Your password has reset,Please look your mailbox',
      name: 'contactAlert4',
      desc: '',
      args: [],
    );
  }

  /// `Your password has changed`
  String get contactAlert5 {
    return Intl.message(
      'Your password has changed',
      name: 'contactAlert5',
      desc: '',
      args: [],
    );
  }

  /// `Forget password`
  String get forgetpwd {
    return Intl.message(
      'Forget password',
      name: 'forgetpwd',
      desc: '',
      args: [],
    );
  }

  /// `Facebook`
  String get facebook {
    return Intl.message(
      'Facebook',
      name: 'facebook',
      desc: '',
      args: [],
    );
  }

  /// `Redbook`
  String get redbook {
    return Intl.message(
      'Redbook',
      name: 'redbook',
      desc: '',
      args: [],
    );
  }

  /// `Tik Tok`
  String get douying {
    return Intl.message(
      'Tik Tok',
      name: 'douying',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message(
      'Logout',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `Bouns`
  String get commission {
    return Intl.message(
      'Bouns',
      name: 'commission',
      desc: '',
      args: [],
    );
  }

  /// `Share & Earn Commission`
  String get commissionTitle {
    return Intl.message(
      'Share & Earn Commission',
      name: 'commissionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Reward Rules`
  String get awardRule {
    return Intl.message(
      'Reward Rules',
      name: 'awardRule',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Mall`
  String get exchangeMall {
    return Intl.message(
      'Exchange Mall',
      name: 'exchangeMall',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Confirmation`
  String get exchangeConfirm {
    return Intl.message(
      'Exchange Confirmation',
      name: 'exchangeConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw`
  String get withdrawCash {
    return Intl.message(
      'Withdraw',
      name: 'withdrawCash',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Record`
  String get withdrawRecord {
    return Intl.message(
      'Withdrawal Record',
      name: 'withdrawRecord',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Record`
  String get exchangeRecord {
    return Intl.message(
      'Exchange Record',
      name: 'exchangeRecord',
      desc: '',
      args: [],
    );
  }

  /// `Points Detail`
  String get pointsDetail {
    return Intl.message(
      'Points Detail',
      name: 'pointsDetail',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Product`
  String get exchangeProduct {
    return Intl.message(
      'Exchange Product',
      name: 'exchangeProduct',
      desc: '',
      args: [],
    );
  }

  /// `Friends List`
  String get friendsList {
    return Intl.message(
      'Friends List',
      name: 'friendsList',
      desc: '',
      args: [],
    );
  }

  /// `Available Points: `
  String get availablePoints {
    return Intl.message(
      'Available Points: ',
      name: 'availablePoints',
      desc: '',
      args: [],
    );
  }

  /// `Quantity:`
  String get quantity {
    return Intl.message(
      'Quantity:',
      name: 'quantity',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get total {
    return Intl.message(
      'Total',
      name: 'total',
      desc: '',
      args: [],
    );
  }

  /// `Heat`
  String get heat {
    return Intl.message(
      'Heat',
      name: 'heat',
      desc: '',
      args: [],
    );
  }

  /// `Points:`
  String get points {
    return Intl.message(
      'Points:',
      name: 'points',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Friends Count: `
  String get friendsCount {
    return Intl.message(
      'Friends Count: ',
      name: 'friendsCount',
      desc: '',
      args: [],
    );
  }

  /// `Exchange`
  String get exchange {
    return Intl.message(
      'Exchange',
      name: 'exchange',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Success`
  String get exchangeSuccess {
    return Intl.message(
      'Exchange Success',
      name: 'exchangeSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Scan Me`
  String get scanMe {
    return Intl.message(
      'Scan Me',
      name: 'scanMe',
      desc: '',
      args: [],
    );
  }

  /// `Mall Info`
  String get mallInfo {
    return Intl.message(
      'Mall Info',
      name: 'mallInfo',
      desc: '',
      args: [],
    );
  }

  /// `Redemption History`
  String get redemptionHistory {
    return Intl.message(
      'Redemption History',
      name: 'redemptionHistory',
      desc: '',
      args: [],
    );
  }

  /// `Reminder Record`
  String get reminderRecord {
    return Intl.message(
      'Reminder Record',
      name: 'reminderRecord',
      desc: '',
      args: [],
    );
  }

  /// `Shared`
  String get shared {
    return Intl.message(
      'Shared',
      name: 'shared',
      desc: '',
      args: [],
    );
  }

  /// `Note: We will complete the audit and processing within 15 working days. Thank you for your cooperation. If you have any questions, please contact customer support at any time.`
  String get withdrawWarning {
    return Intl.message(
      'Note: We will complete the audit and processing within 15 working days. Thank you for your cooperation. If you have any questions, please contact customer support at any time.',
      name: 'withdrawWarning',
      desc: '',
      args: [],
    );
  }

  /// `Exchange Points`
  String get exchangePoints {
    return Intl.message(
      'Exchange Points',
      name: 'exchangePoints',
      desc: '',
      args: [],
    );
  }

  /// `Discount Amount`
  String get discountAmount {
    return Intl.message(
      'Discount Amount',
      name: 'discountAmount',
      desc: '',
      args: [],
    );
  }

  /// `Back to Home`
  String get backToHome {
    return Intl.message(
      'Back to Home',
      name: 'backToHome',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw Success`
  String get withdrawSuccess {
    return Intl.message(
      'Withdraw Success',
      name: 'withdrawSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Withdraw Failed, please try again later`
  String get withdrawFailed {
    return Intl.message(
      'Withdraw Failed, please try again later',
      name: 'withdrawFailed',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message(
      'Name',
      name: 'name',
      desc: '',
      args: [],
    );
  }

  /// `Designation:`
  String get designation {
    return Intl.message(
      'Designation:',
      name: 'designation',
      desc: '',
      args: [],
    );
  }

  /// `Please input name`
  String get nameRequired {
    return Intl.message(
      'Please input name',
      name: 'nameRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please input mobile`
  String get mobileRequired {
    return Intl.message(
      'Please input mobile',
      name: 'mobileRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please input correct mobile`
  String get invalidMobile {
    return Intl.message(
      'Please input correct mobile',
      name: 'invalidMobile',
      desc: '',
      args: [],
    );
  }

  /// `Please input email`
  String get emailRequired {
    return Intl.message(
      'Please input email',
      name: 'emailRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please input correct email`
  String get invalidEmail {
    return Intl.message(
      'Please input correct email',
      name: 'invalidEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please input amount`
  String get amountRequired {
    return Intl.message(
      'Please input amount',
      name: 'amountRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please input correct amount`
  String get amountInvalid {
    return Intl.message(
      'Please input correct amount',
      name: 'amountInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Amount`
  String get amount {
    return Intl.message(
      'Amount',
      name: 'amount',
      desc: '',
      args: [],
    );
  }

  /// `Earn Commission`
  String get earnCommission {
    return Intl.message(
      'Earn Commission',
      name: 'earnCommission',
      desc: '',
      args: [],
    );
  }

  /// `Points`
  String get exchangeablePoints {
    return Intl.message(
      'Points',
      name: 'exchangeablePoints',
      desc: '',
      args: [],
    );
  }

  /// `Accumulative Total`
  String get totalPoints {
    return Intl.message(
      'Accumulative Total',
      name: 'totalPoints',
      desc: '',
      args: [],
    );
  }

  /// `Gift Exchange`
  String get giftExchange {
    return Intl.message(
      'Gift Exchange',
      name: 'giftExchange',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal`
  String get withdrawal {
    return Intl.message(
      'Withdrawal',
      name: 'withdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Points Details`
  String get pointsDetails {
    return Intl.message(
      'Points Details',
      name: 'pointsDetails',
      desc: '',
      args: [],
    );
  }

  /// `Share & Earn`
  String get shareEarnPoints {
    return Intl.message(
      'Share & Earn',
      name: 'shareEarnPoints',
      desc: '',
      args: [],
    );
  }

  /// `Reward Rules`
  String get rewardRules {
    return Intl.message(
      'Reward Rules',
      name: 'rewardRules',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Record`
  String get withdrawalRecord {
    return Intl.message(
      'Withdrawal Record',
      name: 'withdrawalRecord',
      desc: '',
      args: [],
    );
  }

  /// `Customer Service`
  String get customerService {
    return Intl.message(
      'Customer Service',
      name: 'customerService',
      desc: '',
      args: [],
    );
  }

  /// `Not Logged In`
  String get notLoggedIn {
    return Intl.message(
      'Not Logged In',
      name: 'notLoggedIn',
      desc: '',
      args: [],
    );
  }

  /// `After successful redemption, please collect your gift or service at Solidcare Home Improvements store.`
  String get exchangeNotice {
    return Intl.message(
      'After successful redemption, please collect your gift or service at Solidcare Home Improvements store.',
      name: 'exchangeNotice',
      desc: '',
      args: [],
    );
  }

  /// `Address: 16 Esna Park Dr #6, Markham, ON L3R 5X1, Phone: ************`
  String get storeAddress {
    return Intl.message(
      'Address: 16 Esna Park Dr #6, Markham, ON L3R 5X1, Phone: ************',
      name: 'storeAddress',
      desc: '',
      args: [],
    );
  }

  /// `Submit Order`
  String get submitOrder {
    return Intl.message(
      'Submit Order',
      name: 'submitOrder',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal failed, please try again later`
  String get withdrawalFailed {
    return Intl.message(
      'Withdrawal failed, please try again later',
      name: 'withdrawalFailed',
      desc: '',
      args: [],
    );
  }

  /// `Exchange failed, please try again later`
  String get exchangeFailed {
    return Intl.message(
      'Exchange failed, please try again later',
      name: 'exchangeFailed',
      desc: '',
      args: [],
    );
  }

  /// `Commodity`
  String get commodity {
    return Intl.message(
      'Commodity',
      name: 'commodity',
      desc: '',
      args: [],
    );
  }

  /// `introduction to products`
  String get productDesc {
    return Intl.message(
      'introduction to products',
      name: 'productDesc',
      desc: '',
      args: [],
    );
  }

  /// `Order No.: `
  String get orderNumber {
    return Intl.message(
      'Order No.: ',
      name: 'orderNumber',
      desc: '',
      args: [],
    );
  }

  /// `Date: `
  String get date {
    return Intl.message(
      'Date: ',
      name: 'date',
      desc: '',
      args: [],
    );
  }

  /// `User: `
  String get user {
    return Intl.message(
      'User: ',
      name: 'user',
      desc: '',
      args: [],
    );
  }

  /// `Remarks: `
  String get remarks {
    return Intl.message(
      'Remarks: ',
      name: 'remarks',
      desc: '',
      args: [],
    );
  }

  /// `Settled`
  String get settled {
    return Intl.message(
      'Settled',
      name: 'settled',
      desc: '',
      args: [],
    );
  }

  /// `Unsettled`
  String get unsettled {
    return Intl.message(
      'Unsettled',
      name: 'unsettled',
      desc: '',
      args: [],
    );
  }

  /// `Submit: `
  String get submitTime {
    return Intl.message(
      'Submit: ',
      name: 'submitTime',
      desc: '',
      args: [],
    );
  }

  /// `Payment: `
  String get paymentTime {
    return Intl.message(
      'Payment: ',
      name: 'paymentTime',
      desc: '',
      args: [],
    );
  }

  /// `Account: `
  String get account {
    return Intl.message(
      'Account: ',
      name: 'account',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawal Points: `
  String get withdrawalPoints {
    return Intl.message(
      'Withdrawal Points: ',
      name: 'withdrawalPoints',
      desc: '',
      args: [],
    );
  }

  /// `Cash Amount: `
  String get cashAmount {
    return Intl.message(
      'Cash Amount: ',
      name: 'cashAmount',
      desc: '',
      args: [],
    );
  }

  /// `Share Poster`
  String get shareTitle {
    return Intl.message(
      'Share Poster',
      name: 'shareTitle',
      desc: '',
      args: [],
    );
  }

  /// `Solidcare Home Improvements now offers double benefits!`
  String get promotionTitle {
    return Intl.message(
      'Solidcare Home Improvements now offers double benefits!',
      name: 'promotionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Download our App and register as a member now to enjoy up to 5% cash back and earn additional 10% points `
  String get promotionDesc1 {
    return Intl.message(
      'Download our App and register as a member now to enjoy up to 5% cash back and earn additional 10% points ',
      name: 'promotionDesc1',
      desc: '',
      args: [],
    );
  }

  /// `for future redemption or discounts. Save and share this poster with your friends, `
  String get promotionDesc2 {
    return Intl.message(
      'for future redemption or discounts. Save and share this poster with your friends, ',
      name: 'promotionDesc2',
      desc: '',
      args: [],
    );
  }

  /// `invite them to join us and experience the convenience and joy of home improvement while enjoying real benefits!`
  String get promotionDesc3 {
    return Intl.message(
      'invite them to join us and experience the convenience and joy of home improvement while enjoying real benefits!',
      name: 'promotionDesc3',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get saveImage {
    return Intl.message(
      'Save',
      name: 'saveImage',
      desc: '',
      args: [],
    );
  }

  /// `status:`
  String get status {
    return Intl.message(
      'status:',
      name: 'status',
      desc: '',
      args: [],
    );
  }

  /// `No more data`
  String get noMoreData {
    return Intl.message(
      'No more data',
      name: 'noMoreData',
      desc: '',
      args: [],
    );
  }

  /// `Bouns`
  String get shareEarn {
    return Intl.message(
      'Bouns',
      name: 'shareEarn',
      desc: '',
      args: [],
    );
  }

  /// `No image to save`
  String get noImageToSave {
    return Intl.message(
      'No image to save',
      name: 'noImageToSave',
      desc: '',
      args: [],
    );
  }

  /// `Save failed`
  String get saveFailed {
    return Intl.message(
      'Save failed',
      name: 'saveFailed',
      desc: '',
      args: [],
    );
  }

  /// `Image download started`
  String get downloadStarted {
    return Intl.message(
      'Image download started',
      name: 'downloadStarted',
      desc: '',
      args: [],
    );
  }

  /// `Please right-click to save image in new window`
  String get saveInNewWindow {
    return Intl.message(
      'Please right-click to save image in new window',
      name: 'saveInNewWindow',
      desc: '',
      args: [],
    );
  }

  /// `Cannot open new window`
  String get cannotOpenNewWindow {
    return Intl.message(
      'Cannot open new window',
      name: 'cannotOpenNewWindow',
      desc: '',
      args: [],
    );
  }

  /// `Storage permission needed to save image`
  String get needStoragePermission {
    return Intl.message(
      'Storage permission needed to save image',
      name: 'needStoragePermission',
      desc: '',
      args: [],
    );
  }

  /// `Image saved to gallery`
  String get imageSavedToGallery {
    return Intl.message(
      'Image saved to gallery',
      name: 'imageSavedToGallery',
      desc: '',
      args: [],
    );
  }

  /// `Applying`
  String get applying {
    return Intl.message(
      'Applying',
      name: 'applying',
      desc: '',
      args: [],
    );
  }

  /// `Reviewed`
  String get reviewed {
    return Intl.message(
      'Reviewed',
      name: 'reviewed',
      desc: '',
      args: [],
    );
  }

  /// `Paid`
  String get paid {
    return Intl.message(
      'Paid',
      name: 'paid',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled`
  String get cancelled {
    return Intl.message(
      'Cancelled',
      name: 'cancelled',
      desc: '',
      args: [],
    );
  }

  /// `shipped`
  String get shipped {
    return Intl.message(
      'shipped',
      name: 'shipped',
      desc: '',
      args: [],
    );
  }

  /// `Share to earn reward`
  String get hoemShareTitle {
    return Intl.message(
      'Share to earn reward',
      name: 'hoemShareTitle',
      desc: '',
      args: [],
    );
  }

  /// `Permission denied`
  String get permissionReject {
    return Intl.message(
      'Permission denied',
      name: 'permissionReject',
      desc: '',
      args: [],
    );
  }

  /// `If you need to save the file, do you want to enable the permission on the Settings page?`
  String get permissionDesc {
    return Intl.message(
      'If you need to save the file, do you want to enable the permission on the Settings page?',
      name: 'permissionDesc',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get permissionClear {
    return Intl.message(
      'Cancel',
      name: 'permissionClear',
      desc: '',
      args: [],
    );
  }

  /// `Setting`
  String get permissionSetting {
    return Intl.message(
      'Setting',
      name: 'permissionSetting',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'zh', countryCode: 'CN'),
      Locale.fromSubtags(languageCode: 'zh', countryCode: 'HK'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
