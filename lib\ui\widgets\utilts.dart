import 'package:flutter/material.dart';

class Utilts {
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static BoxShadow getBoxSharnd() {
    return BoxShadow(
      color: Colors.black.withOpacity(0.01),
      spreadRadius: 0,
      blurRadius: 4,
      offset: const Offset(0, 2),
    );
  }
}
