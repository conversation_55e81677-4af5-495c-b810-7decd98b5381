import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../generated/l10n.dart';
import 'shared_preferences_util.dart';

/// 语言管理工具类
class LanguageUtil {
  static const String _languageKey = "app_language";

  /// 支持的语言列表
  static const List<String> supportedLanguages = ['en', 'zh_CN', 'zh_HK'];

  /// 获取当前保存的语言设置
  static Future<String?> getSavedLanguage() async {
    return await SharedPreferencesUtil.getInstance().getString(_languageKey);
  }

  /// 保存语言设置
  static Future<void> saveLanguage(String language) async {
    await SharedPreferencesUtil.getInstance().setString(_languageKey, language);
  }

  /// 获取系统当前语言并映射到应用支持的语言
  static String getSystemLanguage() {
    String currentLocale = Intl.getCurrentLocale();

    // 将系统语言映射到应用支持的语言
    if (currentLocale.startsWith('zh')) {
      if (currentLocale.contains('HK') || currentLocale.contains('TW')) {
        return 'zh_HK';
      } else {
        return 'zh_CN';
      }
    } else {
      return 'en';
    }
  }

  /// 初始化语言设置
  /// 优先使用用户保存的设置，如果没有则使用系统语言
  static Future<String> initializeLanguage() async {
    String? savedLanguage = await getSavedLanguage();

    debugPrint("savedLanguage: $savedLanguage");

    if (savedLanguage != null && savedLanguage.isNotEmpty) {
      // 验证保存的语言是否在支持列表中
      if (supportedLanguages.contains(savedLanguage)) {
        return savedLanguage;
      }
    }

    // 如果没有保存的设置或设置无效，使用系统语言
    String systemLanguage = getSystemLanguage();
    await saveLanguage(systemLanguage);
    return systemLanguage;
  }

  /// 切换语言并保存设置
  static Future<void> changeLanguage(String language) async {
    if (supportedLanguages.contains(language)) {
      await saveLanguage(language);
      await S.load(Locale(language));
    }
  }

  /// 根据语言代码获取显示名称
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'zh_CN':
        return '中文简体';
      case 'zh_HK':
        return '中文繁体';
      default:
        return 'English';
    }
  }

  /// 获取当前语言的索引（用于选择器）
  static int getLanguageIndex(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 0;
      case 'zh_CN':
        return 1;
      case 'zh_HK':
        return 2;
      default:
        return 0;
    }
  }

  /// 根据索引获取语言代码（用于选择器）
  static String getLanguageCodeByIndex(int index) {
    switch (index) {
      case 0:
        return 'en';
      case 1:
        return 'zh_CN';
      case 2:
        return 'zh_HK';
      default:
        return 'en';
    }
  }
}
