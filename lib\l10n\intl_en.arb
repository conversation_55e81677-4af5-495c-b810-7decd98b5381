{"@@locale": "en", "home": "Home", "category": "Category", "service": "Service", "order": "Order", "personal": "Personal", "loading": "Loading", "about": "About", "servicearea": "Service Area", "blok": "Blog", "contact": "Contact Us", "contactTye": "Contact Info", "apointment": "Appointment", "goApointment": "Go Apointment", "firstName": "First name", "lastName": "Last name", "address": "Address", "unit": "Unit", "province": "Province", "city": "City", "postCode": "Post code", "phone": "Phone", "mobile": "Mobile", "email": "Email", "orderDate": "Order date", "orderTime": "Order time", "houseYear": "Year of house", "houseArea": "Area of house", "year": "Year", "area": "Square foot", "serviceTitle": "Service options", "productTitle": "Product options", "remarkTitle": "Fill situation of house", "submit": "Submit", "loginAlert": "Please login first", "apointAlert": "Apointment success", "loginTitle": "Sign in for the best experience", "login": "<PERSON><PERSON>", "register": "Register", "unlogin": "Not login", "orderTitle": "Order list", "selfTitle": "Member center", "apointTime": "Apointment time", "preferential": "Preferential", "morepreferential": "How to get more benefits?", "selectLang": "Select language", "regTitle": "Create account.New to solidcare?", "loginName": "Name", "loginMobile": "email", "regPasword": "Create password", "regCode": "Invitation code", "showPassword": "Show password", "continute": "Continue", "regContent": "By creating an account, you agree to silidcare’s Conditions of Use and Privacy Notice", "loginTitle2": "Sign-In.Already a customer?", "loginPassword": "Password", "loginAlert2": "Please input login name and password", "apointOrderDate": "Order date", "apointOrderTime": "Order time", "orderState0": "Uncommitted", "orderState1": "Submitted", "orderState2": "Finish", "orderState3": "Canceled", "orderState4": "Confirmed", "leanmore": "Know more", "shareLink": "Share invitation", "sharePlaceHolder": "Please enter mailbox", "changePwd": "Change password", "changePwdHolder": "Please enter new password", "shareSuccess": "Invitation sent", "regSuccess": "Register success", "contactAlert": "Please input name and email", "contactAlert2": "Submit success", "contactAlert3": "Please input email", "contactAlert4": "Your password has reset,Please look your mailbox", "contactAlert5": "Your password has changed", "forgetpwd": "Forget password", "facebook": "Facebook", "redbook": "Redbook", "douying": "Tik Tok", "logout": "Logout", "commission": "Bouns", "commissionTitle": "Share & Earn Commission", "awardRule": "Reward Rules", "exchangeMall": "Exchange Mall", "exchangeConfirm": "Exchange Confirmation", "withdrawCash": "Withdraw", "withdrawRecord": "<PERSON>drawal Record", "exchangeRecord": "Exchange Record", "pointsDetail": "Points Detail", "exchangeProduct": "Exchange Product", "friendsList": "Friends List", "availablePoints": "Available Points: ", "quantity": "Quantity:", "total": "Total", "heat": "Heat", "points": "Points:", "search": "Search", "friendsCount": "Friends Count: ", "exchange": "Exchange", "exchangeSuccess": "Exchange Success", "scanMe": "<PERSON><PERSON>", "mallInfo": "Mall Info", "redemptionHistory": "Redemption History", "reminderRecord": "Reminder Record", "shared": "Shared", "withdrawWarning": "Note: We will complete the audit and processing within 15 working days. Thank you for your cooperation. If you have any questions, please contact customer support at any time.", "exchangePoints": "Exchange Points", "discountAmount": "Discount Amount", "backToHome": "Back to Home", "withdrawSuccess": "Withdraw Success", "withdrawFailed": "Withdraw Failed, please try again later", "name": "Name", "designation": "Designation:", "nameRequired": "Please input name", "mobileRequired": "Please input mobile", "invalidMobile": "Please input correct mobile", "emailRequired": "Please input email", "invalidEmail": "Please input correct email", "amountRequired": "Please input amount", "amountInvalid": "Please input correct amount", "amount": "Amount", "earnCommission": "Earn Commission", "exchangeablePoints": "Points", "totalPoints": "Accumulative Total", "giftExchange": "Gift Exchange", "withdrawal": "<PERSON><PERSON><PERSON>", "pointsDetails": "Points Details", "shareEarnPoints": "Share & Earn", "rewardRules": "Reward Rules", "withdrawalRecord": "<PERSON>drawal Record", "customerService": "Customer Service", "notLoggedIn": "Not Logged In", "exchangeNotice": "After successful redemption, please collect your gift or service at Solidcare Home Improvements store.", "storeAddress": "Address: 16 Esna Park Dr #6, <PERSON><PERSON>, ON L3R 5X1, Phone: ************", "submitOrder": "Submit Order", "withdrawalFailed": "<PERSON><PERSON><PERSON> failed, please try again later", "exchangeFailed": "Exchange failed, please try again later", "commodity": "Commodity", "productDesc": "introduction to products", "orderNumber": "Order No.: ", "date": "Date: ", "user": "User: ", "remarks": "Remarks: ", "settled": "Settled", "unsettled": "Unsettled", "submitTime": "Submit: ", "paymentTime": "Payment: ", "account": "Account: ", "withdrawalPoints": "Withdrawal Points: ", "cashAmount": "Cash Amount: ", "shareTitle": "Share Poster", "promotionTitle": "Solidcare Home Improvements now offers double benefits!", "promotionDesc1": "Download our App and register as a member now to enjoy up to 5% cash back and earn additional 10% points ", "promotionDesc2": "for future redemption or discounts. Save and share this poster with your friends, ", "promotionDesc3": "invite them to join us and experience the convenience and joy of home improvement while enjoying real benefits!", "saveImage": "Save", "status": "status:", "noMoreData": "No more data", "shareEarn": "Bouns", "noImageToSave": "No image to save", "saveFailed": "Save failed", "downloadStarted": "Image download started", "saveInNewWindow": "Please right-click to save image in new window", "cannotOpenNewWindow": "Cannot open new window", "needStoragePermission": "Storage permission needed to save image", "imageSavedToGallery": "Image saved to gallery", "applying": "Applying", "reviewed": "Reviewed", "paid": "Paid", "cancelled": "Cancelled", "shipped": "shipped", "hoemShareTitle": "Share to earn reward", "permissionReject": "Permission denied", "permissionDesc": "If you need to save the file, do you want to enable the permission on the Settings page?", "permissionClear": "Cancel", "permissionSetting": "Setting"}