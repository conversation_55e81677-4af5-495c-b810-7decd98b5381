import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/http_util.dart';
import '../providers/language_provider.dart';

/// 语言感知的HTTP服务
/// 这个服务可以在有Provider上下文的情况下直接获取当前语言
class LanguageAwareHttpService {
  static final LanguageAwareHttpService _instance =
      LanguageAwareHttpService._internal();
  factory LanguageAwareHttpService() => _instance;
  LanguageAwareHttpService._internal();

  /// 获取当前语言代码（用于API）
  String _convertToApiLanguage(String appLanguageCode) {
    switch (appLanguageCode) {
      case 'zh_CN':
        return 'zh';
      case 'zh_HK':
        return 'es'; // 根据您的代码，繁体中文映射为 'es'
      case 'en':
      default:
        return 'en';
    }
  }

  /// 从Provider获取当前语言（如果有上下文）
  String getCurrentLanguageFromProvider(BuildContext? context) {
    if (context != null) {
      try {
        final languageProvider =
            Provider.of<LanguageProvider>(context, listen: false);
        String currentLang = languageProvider.currentLocale.languageCode;
        if (languageProvider.currentLocale.countryCode != null) {
          currentLang =
              '${languageProvider.currentLocale.languageCode}_${languageProvider.currentLocale.countryCode}';
        }
        return _convertToApiLanguage(currentLang);
      } catch (e) {
        debugPrint('Error getting language from provider: $e');
      }
    }
    return 'en'; // 默认返回英语
  }

  /// 带上下文的GET请求
  Future<dynamic> get(
    String url, {
    Map<String, dynamic>? parameters,
    BuildContext? context,
  }) async {
    parameters ??= <String, dynamic>{};

    // 如果有上下文，优先从Provider获取语言
    if (context != null && url != "/v1/qrcode/get") {
      String lang = getCurrentLanguageFromProvider(context);
      parameters["lang"] = lang;
      debugPrint('Using language from provider: $lang');
    }

    return await HttpUtil.instance.get(url, parameters: parameters);
  }

  /// 带上下文的POST请求
  Future<dynamic> post(
    String url, {
    Map<String, dynamic>? parameters,
    BuildContext? context,
  }) async {
    parameters ??= <String, dynamic>{};

    // 如果有上下文，优先从Provider获取语言
    if (context != null) {
      String lang = getCurrentLanguageFromProvider(context);

      Map params = {};
      if (parameters.containsKey("params")) {
        params = parameters["params"];
      }
      params["lang"] = lang;
      parameters["lang"] = lang;
      parameters["params"] = params;

      debugPrint('Using language from provider: $lang');
    }

    return await HttpUtil.instance.post(url, parameters: parameters);
  }
}
