import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import '../../generated/l10n.dart';
import '../../utils/http_util.dart';

class ArticlePage extends StatefulWidget {
  const ArticlePage({super.key});
  @override
  State<ArticlePage> createState() => _ArticlePageState();
}

class _ArticlePageState extends State<ArticlePage> {
  List services = [];

  @override
  void initState() {
    super.initState();
  }

  //获取服务列表
  Future loadService() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "ProductList";
    parameters["params"] = {"type": "service"};
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  showProductDetail(BuildContext context, index) {
    int productId = services[index]["id"];
    String productTitle = services[index]["productNameLangValue"];
    context.pushNamed(RouteNames.product, pathParameters: {
      'id': productId.toString(),
      'title': productTitle,
    });
  }

  //构造swiper
  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      if (snapshot.data["code"] == 200) {
        List temp = snapshot.data["data"]["list"];
        for (int i = 0; i < temp.length; i++) {
          if (temp[i]["picUrl"] != null && temp[i]["picUrl"].length > 0) {
            services.add(temp[i]);
          }
        }
      }
      return ListView.builder(
        itemCount: services.length,
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
              onTap: () => showProductDetail(context, index),
              child: Container(
                margin: const EdgeInsets.all(8.0),
                height: 200,
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF224195),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8.0),
                            bottomLeft: Radius.circular(8.0),
                          ),
                          image: DecorationImage(
                              image: NetworkImage(services[index]["picUrl"]),
                              fit: BoxFit.cover),
                        ),
                        child: const Align(
                          alignment: Alignment.centerRight,
                          child: Image(
                              image: AssetImage("images/right01.png"),
                              fit: BoxFit.fitHeight),
                        ),
                      ),
                    ),
                    Container(
                      width: 150,
                      decoration: const BoxDecoration(
                          color: Color(0xFF224195),
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(8.0),
                              bottomRight: Radius.circular(8.0)),
                          image: DecorationImage(
                              fit: BoxFit.cover,
                              image: AssetImage("images/right02.png"))),
                      child: Center(
                          child: Text(
                        services[index]["productNameLangValue"] ?? "",
                        textAlign: TextAlign.center,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 20),
                      )),
                    ),
                  ],
                ),
              ));
        },
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(
          S.of(context).service,
          style: const TextStyle(color: Colors.white),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xFFF5F5F5),
        ),
        child: FutureBuilder(future: loadService(), builder: _buildFuture),
      ),
    );
  }
}
