import 'package:flutter_test/flutter_test.dart';
import 'package:new_solidcare/providers/language_provider.dart';
import 'package:new_solidcare/utils/language_util.dart';

void main() {
  group('Simple Language Tests', () {
    test('LanguageProvider should initialize correctly', () async {
      final provider = LanguageProvider();
      
      // 初始状态
      expect(provider.isInitialized, false);
      expect(provider.currentLocale.languageCode, 'en');
      
      // 初始化
      await provider.initializeLanguage();
      
      // 初始化后
      expect(provider.isInitialized, true);
      // 语言应该是支持的语言之一
      final supportedCodes = ['en', 'zh'];
      expect(supportedCodes.contains(provider.currentLocale.languageCode), true);
    });

    test('LanguageUtil should work correctly', () async {
      // 测试系统语言获取
      String systemLang = LanguageUtil.getSystemLanguage();
      expect(LanguageUtil.supportedLanguages.contains(systemLang), true);
      
      // 测试语言显示名称
      expect(LanguageUtil.getLanguageDisplayName('en'), 'English');
      expect(LanguageUtil.getLanguageDisplayName('zh_CN'), '中文简体');
      expect(LanguageUtil.getLanguageDisplayName('zh_HK'), '中文繁体');
      
      // 测试语言索引
      expect(LanguageUtil.getLanguageIndex('en'), 0);
      expect(LanguageUtil.getLanguageIndex('zh_CN'), 1);
      expect(LanguageUtil.getLanguageIndex('zh_HK'), 2);
      
      // 测试根据索引获取语言代码
      expect(LanguageUtil.getLanguageCodeByIndex(0), 'en');
      expect(LanguageUtil.getLanguageCodeByIndex(1), 'zh_CN');
      expect(LanguageUtil.getLanguageCodeByIndex(2), 'zh_HK');
    });

    test('Language change should work', () async {
      final provider = LanguageProvider();
      await provider.initializeLanguage();
      
      // 切换到中文简体
      await provider.changeLanguage('zh_CN');
      expect(provider.currentLocale.toString(), 'zh_CN');
      
      // 切换到英文
      await provider.changeLanguage('en');
      expect(provider.currentLocale.languageCode, 'en');
      
      // 切换到中文繁体
      await provider.changeLanguage('zh_HK');
      expect(provider.currentLocale.toString(), 'zh_HK');
    });
  });
}
