// 定义路由名称常量
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/ui/page/self.dart';
import 'package:new_solidcare/ui/page/home.dart';
import 'package:new_solidcare/ui/page/order.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/ui/page/contact.dart';
import 'package:new_solidcare/ui/page/login.dart';
import 'package:new_solidcare/utils/global.dart';
import 'package:new_solidcare/view_model/base_view.dart';
import 'package:new_solidcare/view_model/main_model.dart';
import 'package:new_solidcare/ui/page/article.dart';
import 'package:new_solidcare/ui/page/appointment.dart';
import 'package:new_solidcare/ui/page/product_detail.dart';
import 'package:new_solidcare/ui/page/article_detail.dart';
import 'package:new_solidcare/ui/page/commission/Redeem.dart';
import 'package:new_solidcare/ui/page/commission/awardRule.dart';
import 'package:new_solidcare/ui/page/commission/commission.dart';
import 'package:new_solidcare/ui/page/commission/exchange.dart';
import 'package:new_solidcare/ui/page/commission/exchangeMall.dart';
import 'package:new_solidcare/ui/page/commission/exchangeSuccess.dart';
import 'package:new_solidcare/ui/page/commission/friends.dart';
import 'package:new_solidcare/ui/page/commission/mallInfo.dart';
import 'package:new_solidcare/ui/page/commission/pointsDetails.dart';
import 'package:new_solidcare/ui/page/commission/redemptionHistory.dart';
import 'package:new_solidcare/ui/page/commission/reminderRecord.dart';
import 'package:new_solidcare/ui/page/commission/shared.dart';
import 'package:new_solidcare/ui/page/commission/withdrawCash.dart';
import 'package:new_solidcare/ui/page/commission/withdrawCashSuccess.dart';

class RouteNames {
  static const String home = 'home';
  static const String article = 'article';
  static const String order = 'order';
  static const String self = 'self';
  static const String product = 'product';
  static const String articleDetail = 'articleDetail';
  static const String login = 'login';
  static const String apointment = 'apointment';
  static const String contact = 'contact';
  static const String commission = 'commission';
  static const String awardrule = 'awardrule';
  static const String exchangemall = 'exchangemall';
  static const String mallinfo = 'mallinfo';
  static const String withdrawcash = 'withdrawcash';
  static const String redeem = 'redeem';
  static const String friends = 'friends';
  static const String pointsDetails = 'pointsDetails';
  static const String redemptionHistory = 'redemptionHistory';
  static const String reminderRecord = 'reminderRecord';
  static const String exchange = 'exchange';
  static const String exchangeSuccess = 'exchangeSuccess';
  static const String withdrawCashSuccess = 'withdrawCashSuccess';
  static const String shared = 'shared';
}

// 定义路由路径常量
class RoutePaths {
  static const String home = '/';
  static const String article = '/article';
  static const String order = '/order';
  static const String self = '/self';
  static const String product = '/product/:id/:title';
  static const String articleDetail = '/article/:id/:type';
  static const String login = '/login/:type';
  static const String apointment = '/apointment/:productId/:orderId';
  static const String contact = '/contact';
  static const String commission = '/commission';
  static const String awardrule = '/awardrule';
  static const String exchangemall = '/exchangemall';
  static const String mallinfo = '/mallinfo/:id';
  static const String withdrawcash = '/withdrawcash';
  static const String redeem = '/redeem/:id';
  static const String friends = '/friends';
  static const String pointsDetails = '/pointsDetails';
  static const String redemptionHistory = '/redemptionHistory';
  static const String reminderRecord = '/reminderRecord';
  static const String exchange = '/exchange/:id';
  static const String exchangeSuccess = '/exchangeSuccess';
  static const String withdrawCashSuccess = '/withdrawCashSuccess';
  static const String shared = '/shared';
}

// 创建路由配置
final GoRouter router = GoRouter(
  initialLocation: RoutePaths.home,
  routes: [
    ShellRoute(
      builder: (context, state, child) {
        // 这里返回你的底部导航栏布局
        return ScaffoldWithNavBar(child: child);
      },
      routes: [
        GoRoute(
          name: RouteNames.home,
          path: RoutePaths.home,
          builder: (context, state) => const HomePage(),
        ),
        GoRoute(
          name: RouteNames.article,
          path: RoutePaths.article,
          builder: (context, state) => const ArticlePage(),
        ),
        GoRoute(
          name: RouteNames.order,
          path: RoutePaths.order,
          builder: (context, state) => OrderPage(),
        ),
        GoRoute(
          name: RouteNames.self,
          path: RoutePaths.self,
          builder: (context, state) => SelfPage(),
        ),
        GoRoute(
          name: RouteNames.contact,
          path: RoutePaths.contact,
          builder: (context, state) => const ContactPage(),
        ),
        GoRoute(
          name: RouteNames.login,
          path: RoutePaths.login,
          builder: (content, state) {
            final id = state.pathParameters['type'] ?? "0";
            int actionType = int.parse(id);
            final code = state.uri.queryParameters['code'] ?? "";
            return LoginPage(
              actionType: actionType,
              code: code,
            );
          },
        )
      ],
    ),
    GoRoute(
      name: RouteNames.product,
      path: RoutePaths.product, // 带参数的路径
      builder: (context, state) {
        // 获取参数
        final id = state.pathParameters['id']!;
        final title = state.pathParameters['title'];
        final productId = int.parse(id);
        return ProductDetailPage(
          productId: productId,
          productTitle: title ?? '',
        );
      },
    ),
    GoRoute(
      name: RouteNames.articleDetail,
      path: RoutePaths.articleDetail,
      builder: (context, state) {
        final id = state.pathParameters['id'] ?? '0';
        final articleId = int.parse(id);
        String type = state.pathParameters['type'] ?? '';
        if (type == '0') {
          type = '';
        }
        return ArticleDetailPage(
          articleId: articleId,
          articleType: type,
        );
      },
    ),
    GoRoute(
      name: RouteNames.apointment,
      path: RoutePaths.apointment,
      builder: (context, state) {
        final pid = state.pathParameters['productId'];
        final oid = state.pathParameters['orderId'];
        final productId = int.parse(pid ?? '0');
        final orderId = int.parse(oid ?? '0');
        return ApointmentPage(
          productId: productId,
          orderId: orderId,
        );
      },
    ),
    GoRoute(
      name: RouteNames.commission,
      path: RoutePaths.commission,
      builder: (context, state) => const Commission(),
    ),
    GoRoute(
      name: RouteNames.awardrule,
      path: RoutePaths.awardrule,
      builder: (context, state) => const AwardRule(),
    ),
    GoRoute(
      name: RouteNames.exchangemall,
      path: RoutePaths.exchangemall,
      builder: (context, state) => const ExchangeMall(),
    ),
    GoRoute(
      name: RouteNames.mallinfo,
      path: RoutePaths.mallinfo,
      builder: (context, state) {
        final id = state.pathParameters['id'];
        final mallId = int.parse(id ?? '0');
        return MallInfo(
          mallId: mallId,
        );
      },
    ),
    GoRoute(
      name: RouteNames.withdrawcash,
      path: RoutePaths.withdrawcash,
      builder: (context, state) => const WithdrawCashPage(),
    ),
    GoRoute(
      name: RouteNames.redeem,
      path: RoutePaths.redeem,
      builder: (context, state) {
        final id = state.pathParameters['id'];
        final mallId = int.parse(id ?? '0');
        return Redeem(
          mallId: mallId,
        );
      },
    ),
    GoRoute(
      name: RouteNames.friends,
      path: RoutePaths.friends,
      builder: (context, state) => const Friends(),
    ),
    GoRoute(
      name: RouteNames.pointsDetails,
      path: RoutePaths.pointsDetails,
      builder: (context, state) => const PointsDetails(),
    ),
    GoRoute(
      name: RouteNames.redemptionHistory,
      path: RoutePaths.redemptionHistory,
      builder: (context, state) => const RedemptionHistory(),
    ),
    GoRoute(
      name: RouteNames.reminderRecord,
      path: RoutePaths.reminderRecord,
      builder: (context, state) => const ReminderRecord(),
    ),
    GoRoute(
      name: RouteNames.exchange,
      path: RoutePaths.exchange,
      builder: (context, state) {
        final id = state.pathParameters['id'];
        final mallId = int.parse(id ?? '0');
        return ExchangePage(
          mallId: mallId,
        );
      },
    ),
    GoRoute(
      name: RouteNames.exchangeSuccess,
      path: RoutePaths.exchangeSuccess,
      builder: (context, state) => const ExchangeSuccessPage(),
    ),
    GoRoute(
      name: RouteNames.withdrawCashSuccess,
      path: RoutePaths.withdrawCashSuccess,
      builder: (context, state) => const WithdrawCashSuccessPage(),
    ),
    GoRoute(
      name: RouteNames.shared,
      path: RoutePaths.shared,
      builder: (context, state) => const SharedPage(),
    ),
  ],
);

// 底部导航栏封装
class ScaffoldWithNavBar extends StatelessWidget {
  final Widget child;

  const ScaffoldWithNavBar({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return BaseView<MainModel>(
      model: MainModel(),
      onModelReady: (model) async {},
      child: child,
      builder: (context, model, child) {
        Global.mainModel = model;
        return Scaffold(
          body: child,
          bottomNavigationBar: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: const Icon(Icons.home),
                label: S.of(context).home,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.design_services),
                label: S.of(context).service,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.list_alt),
                label: S.of(context).order,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.person),
                label: S.of(context).personal,
              ),
            ],
            unselectedItemColor: Colors.grey,
            selectedItemColor: const Color(0xFF224195),
            currentIndex: _calculateSelectedIndex(context),
            onTap: (index) => _onItemTapped(index, context),
          ),
        );
      },
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.toString();
    if (location.startsWith('/product')) {
      final id = GoRouterState.of(context).pathParameters['id'];
      final params = {'id': id.toString(), 'title': 'title'};
      context.pushNamed(RouteNames.product, pathParameters: params);
      return 0;
    }
    if (location.startsWith(RoutePaths.article)) {
      return 1;
    }
    if (location.startsWith(RoutePaths.order)) {
      return 2;
    }
    if (location.startsWith(RoutePaths.self)) {
      return 3;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    if (Global.selectedIndex != index) {
      Global.mainModel.changeSelectIndex(index);
    }
    switch (index) {
      case 0:
        context.goNamed(RouteNames.home);
        break;
      case 1:
        context.goNamed(RouteNames.article);
        break;
      case 2:
        context.goNamed(RouteNames.order);
        break;
      case 3:
        context.goNamed(RouteNames.self);
        break;
    }
  }
}
