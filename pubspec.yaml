name: new_solidcare
description: "A new Flutter project."

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.3

scripts:
  pub: "flutter pub get --no-example"
  run: "flutter run -d chrome --web-renderer html"
  web: "flutter build web --web-renderer html"
  "build": "flutter build apk"
  "build:apk": "flutter build apk --release"
  "build:appbundle": "flutter build appbundle --release"
  "get:packages": "flutter pub get --no-example"
dependencies:
  flutter:
    sdk: flutter
  
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_swiper_null_safety: ^1.0.2
  dio: ^4.0.4
  flutter_html: ^3.0.0-beta.2
  shared_preferences: ^2.0.11
  webviewx_plus: ^0.5.0
  fluttertoast: ^8.2.2
  flutter_datetime_picker_plus: ^2.2.0
  dio_cookie_manager: ^2.0.0
  flutter_picker_plus: ^1.3.0 
  provider: ^6.0.2
  wonderpush_flutter: ^2.3.10
  wonderpush_fcm_flutter: ^1.0.1
  url_launcher_web: ^2.3.3
  url_launcher: ^6.0.20 
  video_player_web: ^2.3.2
  wakelock_web: ^0.4.0
  shelf_proxy: ^1.0.4
  go_router: ^14.6.0
  image_gallery_saver_plus: ^3.0.5
  permission_handler: ^11.0.1
  universal_html: ^2.2.4
  intl_utils: ^2.8.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

flutter:

  uses-material-design: true

  assets:
      - images/bg01.png
      - images/bg02.png
      - images/img.png
      - images/icon-back.png
      - images/icon01.png
      - images/icon02.png
      - images/icon03.png
      - images/icon04.png
      - images/icon05.png
      - images/icon06.png
      - images/icon07.png
      - images/icon08.png
      - images/icon09.png
      - images/icon10.png
      - images/icon11.png
      - images/icon12.png
      - images/icon13.png
      - images/icon14.png
      - images/icon15.png
      - images/icon16.png
      - images/icon17.png
      - images/icon18.png
      - images/img13.png
      - images/line01.png
      - images/welcome.png
      - images/logo.png
      - images/login-in.png
      - images/login-out.png
      - images/img-bg01.png
      - images/img-bg02.png
      - images/right01.png
      - images/right02.png
      - images/list.png
      - images/paid_blue.png
      - images/play_ok.png
      - images/point_green.png
      - images/point_red.png
      - images/shared.png
      - images/success.png
      - images/gift.png
      - images/glod.png
      - images/shared_poster.png
      - images/shared_code.png
      - images/hand.png
      - images/big-zh_CN.png
      - images/big-zh_HK.png
      - images/big-en.png
      - images/ewm-zh_CN.png
      - images/ewm-zh_HK.png
      - images/ewm-en.png
      - images/navBg.png
flutter_intl:
  enabled: true