import 'package:flutter/material.dart';
import '../utils/language_util.dart';
import '../generated/l10n.dart';

/// 语言状态管理类
class LanguageProvider extends ChangeNotifier {
  Locale _currentLocale = const Locale('en');

  Locale get currentLocale => _currentLocale;

  /// 初始化语言设置
  Future<void> initializeLanguage() async {
    try {
      String languageCode = await LanguageUtil.initializeLanguage();

      _currentLocale = Locale(languageCode);

      // 加载对应的国际化资源
      await S.load(_currentLocale);

      // 通知监听者更新UI
      notifyListeners();

      debugPrint('Language initialized: $_currentLocale');
    } catch (e) {
      debugPrint('Error initializing language: $e');
      // 如果出错，使用默认语言
      _currentLocale = const Locale('en');
      await S.load(_currentLocale);
      notifyListeners();
    }
  }

  /// 将应用语言代码转换为API需要的语言代码
  String _convertToApiLanguage(String appLanguageCode) {
    switch (appLanguageCode) {
      case 'zh_CN':
        return 'zh';
      case 'zh_HK':
        return 'es'; // 根据您的代码，繁体中文映射为 'es'
      case 'en':
      default:
        return 'en';
    }
  }

  /// 切换语言
  Future<void> changeLanguage(String languageCode) async {
    try {
      // 保存语言设置
      await LanguageUtil.saveLanguage(languageCode);

      // 更新当前语言
      _currentLocale = Locale(languageCode);

      // 加载对应的国际化资源
      await S.load(_currentLocale);

      // 通知监听者更新UI
      notifyListeners();

      debugPrint('Language changed to: $languageCode');
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }

  /// 获取当前语言的显示名称
  String get currentLanguageDisplayName {
    return LanguageUtil.getLanguageDisplayName(_currentLocale.languageCode);
  }

  /// 获取当前语言在选择器中的索引
  int get currentLanguageIndex {
    String languageCode = _currentLocale.languageCode;
    if (_currentLocale.countryCode != null) {
      languageCode =
          '${_currentLocale.languageCode}_${_currentLocale.countryCode}';
    }
    return LanguageUtil.getLanguageIndex(languageCode);
  }
}
