import 'package:flutter/material.dart';

class RectInput extends TextField {
  RectInput(
      {super.key,
      onChanged,
      hintText,
      suffixIcon,
      maxLines,
      readOnly,
      onTap,
      controller})
      : super(
          controller: controller,
          onChanged: onChanged,
          readOnly: readOnly ?? false,
          onTap: onTap,
          maxLines: maxLines,
          style: const TextStyle(fontSize: 16, color: Colors.black87),
          decoration: InputDecoration(
              fillColor: Colors.white,
              filled: true,
              suffixIcon: suffixIcon,
              hintText: hintText,
              isCollapsed: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(4)),
                borderSide: BorderSide(
                  color: Colors.grey,
                  width: 1.0,
                ),
              )),
        );
}
