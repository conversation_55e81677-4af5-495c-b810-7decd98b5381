import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:fluttertoast/fluttertoast.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/router/router.dart';
import '../../generated/l10n.dart';
import '../../utils/http_util.dart';
import '../../utils/shared_preferences_util.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:universal_html/html.dart' as html;

class LoginPage extends StatefulWidget {
  const LoginPage({super.key, required this.actionType, this.code});
  final int actionType; //类型
  final String? code; //注册时客户名称
  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  // 添加 controller 声明
  late TextEditingController _regCodeController;
  int actionType = 0; //登录类型
  bool showPwd = false;
  String loginName = "";
  String loginPwd = "";
  String regCustomerName = "";
  String regLoginName = "";
  String regPwd = "";
  String regCode = "";
  Widget? curWidget;
  bool isHasCode = false;

  @override
  void initState() {
    super.initState();
    actionType = widget.actionType;
    regCode = widget.code ?? "";
    // 初始化 controller 并设置初始值
    _regCodeController = TextEditingController(text: regCode);
    if (regCode != "") isHasCode = true;
  }

  @override
  void dispose() {
    _regCodeController.dispose();
    super.dispose();
  }

  forgetPwd() {
    if (loginName == "") {
      Fluttertoast.showToast(
          msg: S.of(context).contactAlert3,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP);
      return;
    }
    Map<String, dynamic> parameters = {};
    parameters["email"] = loginName;
    HttpUtil.instance
        .get("/v1/public/forgotPassword", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        Fluttertoast.showToast(
            msg: S.of(context).contactAlert4,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);
      } else {
        Fluttertoast.showToast(
            msg: res["msg"],
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);
      }
    });
  }

  /// 登录
  loginAction() async {
    if (loginName == "" || loginPwd == "") {
      Fluttertoast.showToast(
          msg: S.of(context).loginAlert2,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.TOP);
      return;
    }
    Map<String, dynamic> parameters = {};
    parameters["username"] = loginName;
    parameters["password"] = loginPwd;
    // /v1/user/customerLogin?username=<EMAIL>&password=123456
    HttpUtil.instance
        .get("/v1/user/customerLogin", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        //登录成功
        String userJson = json.encode(res["data"]);
        SharedPreferencesUtil.getInstance().setString("User", userJson);
        context.goNamed(RouteNames.self);
      } else {
        Fluttertoast.showToast(
            msg: res["msg"],
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);
      }
    });
  }

  /// 尝试启动已安装的应用
  Future<bool> _tryLaunchApp(String appScheme) async {
    try {
      if (await canLaunch(appScheme)) {
        await launch(appScheme);
        return true; // 成功启动应用
      }
    } catch (e) {
      // 尝试启动应用失败，可能是因为应用未安装
      return false;
    }
    return false; // 应用未安装或启动失败
  }

  /// 判断设备类型并重定向
  Future<void> _launchAppStore(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Fluttertoast.showToast(
          msg: "无法打开链接: $url",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM);
    }
  }

  /// 检测设备类型并重定向
  Future<void> detectDeviceAndRedirect() async {
    if (!kIsWeb) {
      // 如果不是Web平台，直接跳转到首页
      context.goNamed(RouteNames.self);
      return;
    }

    try {
      final userAgent = html.window.navigator.userAgent.toLowerCase();

      if (userAgent.contains('iphone') ||
          userAgent.contains('ipad') ||
          userAgent.contains('ipod')) {
        // iOS设备
        // 尝试启动应用，如果无法启动则跳转到App Store
        final bool appLaunched = await _tryLaunchApp('solidcare://');
        if (!appLaunched) {
          _launchAppStore(
              'https://apps.apple.com/us/app/solidcare/id1603785978');
        }
      } else if (userAgent.contains('android')) {
        // 安卓设备
        // 尝试启动应用，如果无法启动则跳转到下载链接
        final bool appLaunched = await _tryLaunchApp('solidcare://');
        if (!appLaunched) {
          _launchAppStore('http://solidcareweb.us.luckycrm.com/app/bwm.apk');
        }
      } else {
        // 其他设备，跳转到首页
        context.goNamed(RouteNames.self);
      }
    } catch (e) {
      // 异常处理，显示错误并跳转到首页
      Fluttertoast.showToast(
          msg: "检测设备出错: ${e.toString()}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM);
      context.goNamed(RouteNames.self);
    }
  }

  registerAction() {
    Map<String, dynamic> parameters = {};
    parameters["customerName"] = regCustomerName;
    parameters["loginName"] = regLoginName;
    parameters["password"] = regPwd;
    parameters["code"] = regCode;
    HttpUtil.instance
        .post("/v1/user/register", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        //注册成功
        actionType = 0;
        Fluttertoast.showToast(
            msg: S.of(context).regSuccess,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);

        // 注册成功后检测设备类型并进行相应重定向
        detectDeviceAndRedirect();
      } else {
        Fluttertoast.showToast(
            msg: res["msg"],
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(
          S.of(context).selfTitle,
          style: const TextStyle(color: Colors.white),
        ),
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(40),
              child: const Center(
                child: Image(
                  width: 200,
                  image: AssetImage("images/welcome.png"),
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(
                left: 30,
                top: 15,
                right: 30,
                bottom: 15,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                border: Border.all(width: 1, color: Colors.grey),
              ),
              child: Column(
                children: [
                  buildLoginPage(),
                  buildRegPage(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildLoginPage() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: actionType == 0 ? const Color(0xFFDCDCDC) : Colors.white,
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7.0), topRight: Radius.circular(7.0)),
          ),
          child: Row(
            children: [
              Radio(
                  value: actionType,
                  toggleable: true,
                  groupValue: 1,
                  onChanged: (v) {
                    setState(() {
                      actionType = 1;
                    });
                  }),
              Text(S.of(context).regTitle)
            ],
          ),
        ),
        Visibility(
          visible: actionType == 0,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  onChanged: (v) {
                    setState(() {
                      loginName = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: InputDecoration(
                      hintText: S.of(context).loginMobile,
                      isCollapsed: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 10),
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  onChanged: (v) {
                    setState(() {
                      loginPwd = v;
                    });
                  },
                  obscureText: true,
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: InputDecoration(
                      hintText: S.of(context).loginPassword,
                      isCollapsed: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 10),
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: SizedBox(
                    width: double.infinity, // 宽度无限，跟父控件保持一致
                    child: ButtonTheme(
                        height: 50.0,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.all(18.0),
                            backgroundColor:
                                const Color(0xFF224195), // Background color
                          ),
                          child: Text(
                            S.of(context).continute,
                            style: const TextStyle(color: Colors.white),
                          ),
                          onPressed: () async {
                            loginAction();
                          },
                        ))),
              ),
              Container(
                padding: const EdgeInsets.only(bottom: 10),
                child: GestureDetector(
                  onTap: () => forgetPwd(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        S.of(context).forgetpwd,
                        style: const TextStyle(
                          decoration: TextDecoration.underline,
                        ),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget buildRegPage() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: actionType == 1 ? const Color(0xFFDCDCDC) : Colors.white,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(7.0),
              bottomRight: Radius.circular(7.0),
            ),
          ),
          child: Row(
            children: [
              Radio(
                value: actionType,
                toggleable: true,
                groupValue: 0,
                onChanged: (v) {
                  setState(() {
                    actionType = 0;
                  });
                },
              ),
              Text(S.of(context).loginTitle2)
            ],
          ),
        ),
        Visibility(
          visible: actionType == 1,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  onChanged: (v) {
                    setState(() {
                      regCustomerName = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: InputDecoration(
                    hintText: S.of(context).loginName,
                    isCollapsed: true,
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                      borderSide: BorderSide(
                        color: Colors.grey,
                        width: 1.0,
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  onChanged: (v) {
                    setState(() {
                      regLoginName = v;
                    });
                  },
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: InputDecoration(
                    hintText: S.of(context).loginMobile,
                    isCollapsed: true,
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                      borderSide: BorderSide(
                        color: Colors.grey,
                        width: 1.0,
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  onChanged: (v) {
                    setState(() {
                      regPwd = v;
                    });
                  },
                  obscureText: !showPwd,
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  decoration: InputDecoration(
                      hintText: S.of(context).regPasword,
                      isCollapsed: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 10),
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        borderSide: BorderSide(
                          color: Colors.grey,
                          width: 1.0,
                        ),
                      )),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: TextField(
                  controller: _regCodeController, // 使用 controller
                  enabled: !isHasCode, // 当 regCode 为空时才允许编辑
                  onChanged: (v) {
                    setState(() {
                      regCode = v;
                    });
                  },
                  style: TextStyle(
                    fontSize: 16,
                    color: isHasCode ? Colors.grey : Colors.black87,
                  ),
                  decoration: InputDecoration(
                    hintText: S.of(context).regCode,
                    isCollapsed: true,
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                      borderSide: BorderSide(
                        color: Colors.grey,
                        width: 1.0,
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: Row(
                  children: [
                    Checkbox(
                        value: showPwd,
                        onChanged: (v) {
                          setState(() {
                            showPwd = !showPwd;
                          });
                        }),
                    Text(S.of(context).showPassword)
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(10),
                child: SizedBox(
                  width: double.infinity, // 宽度无限，跟父控件保持一致
                  child: ButtonTheme(
                    height: 50.0,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(18.0),
                        backgroundColor:
                            const Color(0xFF224195), // Background color
                      ),
                      child: Text(
                        S.of(context).continute,
                        style: const TextStyle(color: Colors.white),
                      ),
                      onPressed: () async {
                        registerAction();
                      },
                    ),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(15),
                child: Text(S.of(context).regContent),
              )
            ],
          ),
        ),
      ],
    );
  }
}
