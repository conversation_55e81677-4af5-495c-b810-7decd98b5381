import 'package:flutter/foundation.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:new_solidcare/view_model/main_model.dart';

class IntergralModel {
  double customerIntegral;
  double frozenIntegral;
  double settlementIntegral;
  int interal; // 新增字段
  int useInteral; // 新增字段

  IntergralModel({
    this.customerIntegral = 0,
    this.frozenIntegral = 0,
    this.settlementIntegral = 0,
    this.interal = 0,
    this.useInteral = 0,
  });

  factory IntergralModel.fromJson(Map<String, dynamic> json) {
    var customerIntegral = json['customerIntegral'] ?? 0;
    var frozenIntegral = json['frozenIntegral'] ?? 0;
    var settlementIntegral = json['settlementIntegral'] ?? 0;
    var interal = customerIntegral - frozenIntegral;
    var useInteral = settlementIntegral + customerIntegral;
    return IntergralModel(
      customerIntegral: customerIntegral,
      frozenIntegral: frozenIntegral,
      settlementIntegral: settlementIntegral,
      interal: interal.toInt(), // 计算 interal
      useInteral: useInteral.toInt(), // 计算 useInteral
    );
  }
}

class Global {
  static int selectedIndex = 0;
  static late MainModel mainModel;

  /// 获取积分
  static Future<IntergralModel?> getIntergral() async {
    Map<String, dynamic> parameters = {};
    parameters["params"] = {};
    var res = await HttpUtil.instance
        .post("/v1/customer/getIntegral", parameters: parameters);
    if (res["code"] == 200) {
      return IntergralModel.fromJson(res['data']);
    }
    return null;
  }
}
