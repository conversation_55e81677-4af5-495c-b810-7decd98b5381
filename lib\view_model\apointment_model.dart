import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:new_solidcare/view_model/base_model.dart';
import '../utils/http_util.dart';

class ApointmentModel extends BaseModel {
  Map _apointment = {
    "firstName": "",
    "lastName": "",
    "address": "",
    "unit": "",
    "city": "",
    "province": null,
    "postCode": "",
    "phone": "",
    "mobile": "",
    "email": "",
    "houseYear": "",
    "area": "",
    "orderDate": "",
    "orderTime": "",
    "remark": ""
  };
  List _service = [];
  List _product = [];
  List _province = [];

  List get service => _service;

  List get product => _product;

  List get province => _province;

  Map get apointment => _apointment;

  /// 修改服务选状态
  changeServiceCheck(int index, bool? isCheck) {
    if (_service.length > index) {
      _service[index]["checked"] = isCheck;
      notifyListeners();
    }
  }

  /// 加载数据
  loadApointment(int orderId) {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Detail";
    parameters["params"] = {"id": orderId.toString()};
    HttpUtil.instance
        .post("/v1/order/query", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        _apointment = res["data"]["list"][0];
        DateTime orderTime = DateTime.parse(_apointment["orderDate"]);
        _apointment["orderDate"] = DateFormat('yyyy-MM-dd').format(orderTime);
        _apointment["orderTime"] = DateFormat('HH:mm:ss').format(orderTime);
        _apointment["houseYear"] = _apointment["houseYear"] ?? "";
        _apointment["unit"] = _apointment["unit"] ?? "";
        _apointment["city"] = _apointment["city"] ?? "";
        _apointment["postCode"] = _apointment["postCode"] ?? "";
        _apointment["phone"] = _apointment["phone"] ?? "";
        _apointment["email"] = _apointment["email"] ?? "";
        notifyListeners();
      }
    });
    parameters = <String, dynamic>{};
    parameters["query"] = "OrderDetail";
    parameters["params"] = {"id": orderId.toString()};
    HttpUtil.instance
        .post("/v1/order/query", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        List list = res["data"]["list"];
        for (int i = 0; i < list.length; i++) {
          var productId = list[i]["productId"];
          for (int j = 0; j < _service.length; j++) {
            if (_service[j]["id"] == productId) {
              _service[j]["checked"] = true;
            }
          }
          for (int j = 0; j < product.length; j++) {
            if (product[j]["id"] == productId) {
              product[j]["checked"] = true;
            }
          }
        }
        notifyListeners();
      }
    });
  }

  /// 修改服务选状态
  changeProductCheck(int index, bool? isCheck) {
    if (product.length > index) {
      product[index]["checked"] = isCheck;
      notifyListeners();
    }
  }

  /// 修改订单日期
  changeOrderDate(DateTime date) {
    String orderDate = DateFormat('yyyy-MM-dd').format(date);
    if (orderDate != _apointment["orderDate"]) {
      _apointment["orderDate"] = orderDate;
      notifyListeners();
    }
  }

  /// 修改订单日期
  changeOrderTime(DateTime date) {
    String orderTime = DateFormat('HH:mm:ss').format(date);
    if (orderTime != _apointment["orderTime"]) {
      _apointment["orderTime"] = orderTime;
      notifyListeners();
    }
  }

  /// 修改省份
  changeProvince(Object? province) {
    if (province != _apointment["province"]) {
      _apointment["province"] = province;
      notifyListeners();
    }
  }

  /// 保存订单
  Future saveApointment() async {
    List selectItems = [];
    _apointment["companyId"] = 1;
    _apointment["state"] = 1; //直接提交订单
    if (_apointment["orderTime"] != null &&
        _apointment["orderTime"].length > 0) {
      _apointment["orderDate"] =
          _apointment["orderDate"] + " " + _apointment["orderTime"];
    }
    for (int i = 0; i < _service.length; i++) {
      if (_service[i]["checked"]) {
        selectItems.add({"productId": _service[i]["id"], "qty": 1});
      }
    }
    for (int i = 0; i < _product.length; i++) {
      if (_product[i]["checked"]) {
        selectItems.add({"productId": _product[i]["id"], "qty": 1});
      }
    }
    Map<String, dynamic> data = {"order": _apointment, "details": selectItems};
    return HttpUtil.instance.post("/v1/order/save", parameters: data);
  }

  /// 加载服务与产品数据
  Future<bool> loadData(int productId) async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "ProductList";
    parameters["params"] = {"type": "service"};
    setState(ViewState.Busy);
    await HttpUtil.instance
        .post("/v1/public/query", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        _service = res["data"]["list"];
        for (var i = 0; i < _service.length; i++) {
          if (_service[i]["checked"] == null) {
            _service[i]["checked"] = false;
          }
          if (_service[i]["id"] == productId) {
            _service[i]["checked"] = true;
          }
        }
      }
    });
    parameters = {};
    parameters["query"] = "ProductList";
    parameters["params"] = {"type": "product"};
    await HttpUtil.instance
        .post("/v1/public/query", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        _product = res["data"]["list"];
        for (var i = 0; i < _product.length; i++) {
          if (_product[i]["checked"] == null) {
            _product[i]["checked"] = false;
          }
        }
      }
    });
    parameters = {};
    parameters["query"] = "ListInfo";
    parameters["params"] = {"type": "province"};
    await HttpUtil.instance
        .post("/v1/public/query", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        _province = res["data"]["list"];
      }
    });
    setState(ViewState.Idle);
    return true;
  }
}
