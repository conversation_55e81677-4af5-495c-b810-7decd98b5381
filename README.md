# new_solidcare

保温棉二期

## Getting Started

[项目二期文档](https://docs.qq.com/sheet/DVW53RENXaGJQSkZy?tab=kxo3c2) <br />
[文档字段说明](https://docs.qq.com/doc/DVUJ4dWpQWW96bFFL) <br />
[新文档](https://modao.cc/proto/8XLo4h6usnjaw4rIX6CoDj/sharing?view_mode=read_only) <br />
[三期文档](https://modao.cc/proto/zr8Jsgn9snj9qrdmcbx7hO/sharing?view_mode=read_only) <br />
[小改](https://docs.qq.com/doc/DVXJ1Y3BOTnp3TU9M) <br />
[小改2](https://docs.qq.com/doc/DVXJ1Y3BOTnp3TU9M)<br />
[海报分享](https://docs.qq.com/doc/DVVVIanpzcGV2ZXdY) <br />
[问题](https://docs.qq.com/sheet/DVVN4a1VsaHhFTlNO?tab=8mvblz) <br />
[问题2](https://docs.qq.com/sheet/DVVN4a1VsaHhFTlNO?tab=8mvblz) <br />
[前端接口解释]( https://docs.qq.com/doc/DVWlUZkNJWmR0cnRS) <br />

[APISwagger](http://bwm.leazy.cn:30000/swagger-ui.html) <br />
[项目后台](http://bwm.leazy.cn:3600/)
账号：admin
密码：123456
irm https://raw.githubusercontent.com/yuaotian/go-cursor-help/master/scripts/install.ps1 | iex

<EMAIL>
123456

<EMAIL>
Solidapp2k25$

416-267-2818

IOS地址：https://apps.apple.com/cn/app/solidcare/id1603785978
Google play
https://play.google.com/console/u/2/developers/4877518320148068172/app-list?onboardingflow=signup
<EMAIL>
Solid918

Google Play Consle
https://developer.android.com/
开发者账号：<EMAIL>
密码：solicarehome918

Apple Developer  
https://developer.apple.com/
开发者账号：
<EMAIL>
​密码：Aasdf1234@

flutter build ios
App线上网址：http://solidcareweb.us.luckycrm.com


export PUB_HOSTED_URL=https://pub.flutter-io.cn;
export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn

$env:PUB_HOSTED_URL="https://pub.flutter-io.cn"
$env:FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"