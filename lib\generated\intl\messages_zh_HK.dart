// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_HK locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_HK';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("關於我們"),
        "account": MessageLookupByLibrary.simpleMessage("賬號: "),
        "address": MessageLookupByLibrary.simpleMessage("地址"),
        "amount": MessageLookupByLibrary.simpleMessage("金額"),
        "amountInvalid": MessageLookupByLibrary.simpleMessage("請輸入正確的金額"),
        "amountRequired": MessageLookupByLibrary.simpleMessage("請輸入金額"),
        "apointAlert": MessageLookupByLibrary.simpleMessage("預約成功"),
        "apointOrderDate": MessageLookupByLibrary.simpleMessage("可預約日期"),
        "apointOrderTime": MessageLookupByLibrary.simpleMessage("可預約時間"),
        "apointTime": MessageLookupByLibrary.simpleMessage("預約時間"),
        "apointment": MessageLookupByLibrary.simpleMessage("預約"),
        "applying": MessageLookupByLibrary.simpleMessage("申請中"),
        "area": MessageLookupByLibrary.simpleMessage("平方英尺"),
        "availablePoints": MessageLookupByLibrary.simpleMessage("可用積分："),
        "awardRule": MessageLookupByLibrary.simpleMessage("獎勵規則"),
        "backToHome": MessageLookupByLibrary.simpleMessage("返回首頁"),
        "blok": MessageLookupByLibrary.simpleMessage("博客"),
        "cancelled": MessageLookupByLibrary.simpleMessage("作廢"),
        "cannotOpenNewWindow": MessageLookupByLibrary.simpleMessage("無法打開新窗口"),
        "cashAmount": MessageLookupByLibrary.simpleMessage("折現金額: "),
        "category": MessageLookupByLibrary.simpleMessage("分類"),
        "changePwd": MessageLookupByLibrary.simpleMessage("修改密碼"),
        "changePwdHolder": MessageLookupByLibrary.simpleMessage("請輸入新密碼"),
        "city": MessageLookupByLibrary.simpleMessage("城市"),
        "commission": MessageLookupByLibrary.simpleMessage("分享賺佣金"),
        "commissionTitle": MessageLookupByLibrary.simpleMessage("分享賺佣金"),
        "commodity": MessageLookupByLibrary.simpleMessage("商品"),
        "contact": MessageLookupByLibrary.simpleMessage("聯繫我們"),
        "contactAlert": MessageLookupByLibrary.simpleMessage("請輸入郵箱和密碼"),
        "contactAlert2": MessageLookupByLibrary.simpleMessage("提交成功"),
        "contactAlert3": MessageLookupByLibrary.simpleMessage("請輸入郵箱"),
        "contactAlert4": MessageLookupByLibrary.simpleMessage("您的密碼已重置,請查看郵箱"),
        "contactAlert5": MessageLookupByLibrary.simpleMessage("您的密碼已經修改"),
        "contactTye": MessageLookupByLibrary.simpleMessage("聯繫方式"),
        "continute": MessageLookupByLibrary.simpleMessage("確定"),
        "customerService": MessageLookupByLibrary.simpleMessage("撥打客服電話"),
        "date": MessageLookupByLibrary.simpleMessage("日期："),
        "designation": MessageLookupByLibrary.simpleMessage("名稱："),
        "discountAmount": MessageLookupByLibrary.simpleMessage("折現金額"),
        "douying": MessageLookupByLibrary.simpleMessage("抖音"),
        "downloadStarted": MessageLookupByLibrary.simpleMessage("圖片開始下載"),
        "earnCommission": MessageLookupByLibrary.simpleMessage("分享賺佣金"),
        "email": MessageLookupByLibrary.simpleMessage("郵箱"),
        "emailRequired": MessageLookupByLibrary.simpleMessage("請輸入郵箱"),
        "exchange": MessageLookupByLibrary.simpleMessage("兌換"),
        "exchangeConfirm": MessageLookupByLibrary.simpleMessage("兌換確認"),
        "exchangeFailed": MessageLookupByLibrary.simpleMessage("兌換失敗，請稍後再試"),
        "exchangeMall": MessageLookupByLibrary.simpleMessage("兌換商城"),
        "exchangeNotice": MessageLookupByLibrary.simpleMessage(
            "成功兌換後，請到Solidcare Home Improvements 公司門店領取您的禮品或服務。"),
        "exchangePoints": MessageLookupByLibrary.simpleMessage("兌換積分"),
        "exchangeProduct": MessageLookupByLibrary.simpleMessage("兌換商品"),
        "exchangeRecord": MessageLookupByLibrary.simpleMessage("兌換記錄"),
        "exchangeSuccess": MessageLookupByLibrary.simpleMessage("兌換成功"),
        "exchangeablePoints": MessageLookupByLibrary.simpleMessage("可兌換積分"),
        "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
        "firstName": MessageLookupByLibrary.simpleMessage("名"),
        "forgetpwd": MessageLookupByLibrary.simpleMessage("忘記密碼"),
        "friendsCount": MessageLookupByLibrary.simpleMessage("好友數: "),
        "friendsList": MessageLookupByLibrary.simpleMessage("好友列表"),
        "giftExchange": MessageLookupByLibrary.simpleMessage("禮品兌換"),
        "goApointment": MessageLookupByLibrary.simpleMessage("前往預約"),
        "heat": MessageLookupByLibrary.simpleMessage("熱度"),
        "hoemShareTitle": MessageLookupByLibrary.simpleMessage("分享賺佣金"),
        "home": MessageLookupByLibrary.simpleMessage("首頁"),
        "houseArea": MessageLookupByLibrary.simpleMessage("房子的大概面積"),
        "houseYear": MessageLookupByLibrary.simpleMessage("房子的大概房齡"),
        "imageSavedToGallery": MessageLookupByLibrary.simpleMessage("圖片已保存到相冊"),
        "invalidEmail": MessageLookupByLibrary.simpleMessage("請輸入正確的郵箱"),
        "invalidMobile": MessageLookupByLibrary.simpleMessage("請輸入正確的手機號"),
        "lastName": MessageLookupByLibrary.simpleMessage("姓"),
        "leanmore": MessageLookupByLibrary.simpleMessage("了解詳情"),
        "loading": MessageLookupByLibrary.simpleMessage("加載中"),
        "login": MessageLookupByLibrary.simpleMessage("登      錄"),
        "loginAlert": MessageLookupByLibrary.simpleMessage("請先登錄"),
        "loginAlert2": MessageLookupByLibrary.simpleMessage("請輸入登錄名與密碼"),
        "loginMobile": MessageLookupByLibrary.simpleMessage("郵箱"),
        "loginName": MessageLookupByLibrary.simpleMessage("用戶名"),
        "loginPassword": MessageLookupByLibrary.simpleMessage("密碼"),
        "loginTitle": MessageLookupByLibrary.simpleMessage("登錄以獲取最佳體驗"),
        "loginTitle2": MessageLookupByLibrary.simpleMessage("已有賬號,請登錄?"),
        "logout": MessageLookupByLibrary.simpleMessage("登出"),
        "mallInfo": MessageLookupByLibrary.simpleMessage("商城資訊"),
        "mobile": MessageLookupByLibrary.simpleMessage("行動電話"),
        "mobileRequired": MessageLookupByLibrary.simpleMessage("請輸入手機號"),
        "morepreferential": MessageLookupByLibrary.simpleMessage("如何獲取更多優惠?"),
        "name": MessageLookupByLibrary.simpleMessage("姓名"),
        "nameRequired": MessageLookupByLibrary.simpleMessage("請輸入姓名"),
        "needStoragePermission":
            MessageLookupByLibrary.simpleMessage("需要存儲權限來保存圖片"),
        "noImageToSave": MessageLookupByLibrary.simpleMessage("沒有可保存的圖片"),
        "noMoreData": MessageLookupByLibrary.simpleMessage("沒有更多數據了"),
        "notLoggedIn": MessageLookupByLibrary.simpleMessage("未登錄"),
        "order": MessageLookupByLibrary.simpleMessage("訂單"),
        "orderDate": MessageLookupByLibrary.simpleMessage("預約日期"),
        "orderNumber": MessageLookupByLibrary.simpleMessage("訂單號: "),
        "orderState0": MessageLookupByLibrary.simpleMessage("未提交"),
        "orderState1": MessageLookupByLibrary.simpleMessage("已提交"),
        "orderState2": MessageLookupByLibrary.simpleMessage("已完成"),
        "orderState3": MessageLookupByLibrary.simpleMessage("已取消"),
        "orderState4": MessageLookupByLibrary.simpleMessage("已確認"),
        "orderTime": MessageLookupByLibrary.simpleMessage("預約時間"),
        "orderTitle": MessageLookupByLibrary.simpleMessage("訂單列表"),
        "paid": MessageLookupByLibrary.simpleMessage("已支付"),
        "paymentTime": MessageLookupByLibrary.simpleMessage("支付時間: "),
        "permissionClear": MessageLookupByLibrary.simpleMessage("取消"),
        "permissionDesc":
            MessageLookupByLibrary.simpleMessage("需要存儲權限以保存文件，是否去設置頁面開啟權限？"),
        "permissionReject": MessageLookupByLibrary.simpleMessage("權限被拒絕"),
        "permissionSetting": MessageLookupByLibrary.simpleMessage("去設置"),
        "personal": MessageLookupByLibrary.simpleMessage("我的"),
        "phone": MessageLookupByLibrary.simpleMessage("家庭電話"),
        "points": MessageLookupByLibrary.simpleMessage("積分："),
        "pointsDetail": MessageLookupByLibrary.simpleMessage("積分明細"),
        "pointsDetails": MessageLookupByLibrary.simpleMessage("積分明細"),
        "postCode": MessageLookupByLibrary.simpleMessage("郵政編號"),
        "preferential": MessageLookupByLibrary.simpleMessage("優惠信息"),
        "productDesc": MessageLookupByLibrary.simpleMessage("產品簡介"),
        "productTitle": MessageLookupByLibrary.simpleMessage("產品選項"),
        "promotionDesc1": MessageLookupByLibrary.simpleMessage(
            "立即下載我們的App並註冊成為會員，不僅可以享受高達5%的現金返還，還能額外累積10%的積分，"),
        "promotionDesc2":
            MessageLookupByLibrary.simpleMessage("用於未來的兌換或折扣。將此海報保存並分享給您的好友，"),
        "promotionDesc3": MessageLookupByLibrary.simpleMessage(
            "邀請他們一起加入，共同體驗家居改善的便捷與樂趣，享受實實在在的優惠！"),
        "promotionTitle": MessageLookupByLibrary.simpleMessage(
            "Solidcare Home Improvements 現推出雙重優惠！"),
        "province": MessageLookupByLibrary.simpleMessage("省份"),
        "quantity": MessageLookupByLibrary.simpleMessage("數量："),
        "redbook": MessageLookupByLibrary.simpleMessage("小紅書"),
        "redemptionHistory": MessageLookupByLibrary.simpleMessage("兌換記錄"),
        "regCode": MessageLookupByLibrary.simpleMessage("邀請碼"),
        "regContent":
            MessageLookupByLibrary.simpleMessage("註冊一個新的賬號, 您需要同意相關條款"),
        "regPasword": MessageLookupByLibrary.simpleMessage("創建密碼"),
        "regSuccess": MessageLookupByLibrary.simpleMessage("註冊成功"),
        "regTitle": MessageLookupByLibrary.simpleMessage("註冊一個新的賬號?"),
        "register": MessageLookupByLibrary.simpleMessage("創建會員"),
        "remarkTitle": MessageLookupByLibrary.simpleMessage("填寫房子的保溫情況"),
        "remarks": MessageLookupByLibrary.simpleMessage("備註："),
        "reminderRecord": MessageLookupByLibrary.simpleMessage("提醒記錄"),
        "reviewed": MessageLookupByLibrary.simpleMessage("已審核"),
        "rewardRules": MessageLookupByLibrary.simpleMessage("獎勵規則"),
        "saveFailed": MessageLookupByLibrary.simpleMessage("保存失敗"),
        "saveImage": MessageLookupByLibrary.simpleMessage("保存圖片"),
        "saveInNewWindow": MessageLookupByLibrary.simpleMessage("請在新窗口中右鍵保存圖片"),
        "scanMe": MessageLookupByLibrary.simpleMessage("掃一掃我"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "selectLang": MessageLookupByLibrary.simpleMessage("語言選擇"),
        "selfTitle": MessageLookupByLibrary.simpleMessage("會員中心"),
        "service": MessageLookupByLibrary.simpleMessage("服務"),
        "serviceTitle": MessageLookupByLibrary.simpleMessage("服務選項"),
        "servicearea": MessageLookupByLibrary.simpleMessage("服務區域"),
        "settled": MessageLookupByLibrary.simpleMessage("已結算"),
        "shareEarn": MessageLookupByLibrary.simpleMessage("分享拿佣金"),
        "shareEarnPoints": MessageLookupByLibrary.simpleMessage("分享賺積分"),
        "shareLink": MessageLookupByLibrary.simpleMessage("分享邀請"),
        "sharePlaceHolder": MessageLookupByLibrary.simpleMessage("請輸入分享郵箱地址"),
        "shareSuccess": MessageLookupByLibrary.simpleMessage("已發邀請"),
        "shareTitle": MessageLookupByLibrary.simpleMessage("分享海報"),
        "shared": MessageLookupByLibrary.simpleMessage("已分享"),
        "shipped": MessageLookupByLibrary.simpleMessage("已發貨"),
        "showPassword": MessageLookupByLibrary.simpleMessage("顯示密碼"),
        "status": MessageLookupByLibrary.simpleMessage("狀態："),
        "storeAddress": MessageLookupByLibrary.simpleMessage(
            "地址：Address:16 Esna Park Dr #6, Markham, ON L3R 5X1，Phone: ************"),
        "submit": MessageLookupByLibrary.simpleMessage("立即提交"),
        "submitOrder": MessageLookupByLibrary.simpleMessage("提交訂單"),
        "submitTime": MessageLookupByLibrary.simpleMessage("提交時間: "),
        "total": MessageLookupByLibrary.simpleMessage("合計"),
        "totalPoints": MessageLookupByLibrary.simpleMessage("累計積分"),
        "unit": MessageLookupByLibrary.simpleMessage("單元"),
        "unlogin": MessageLookupByLibrary.simpleMessage("未登錄"),
        "unsettled": MessageLookupByLibrary.simpleMessage("未結算"),
        "user": MessageLookupByLibrary.simpleMessage("用戶："),
        "withdrawCash": MessageLookupByLibrary.simpleMessage("提現"),
        "withdrawFailed": MessageLookupByLibrary.simpleMessage("提現失敗，請稍後再試"),
        "withdrawRecord": MessageLookupByLibrary.simpleMessage("提現記錄"),
        "withdrawSuccess": MessageLookupByLibrary.simpleMessage("提現成功"),
        "withdrawWarning": MessageLookupByLibrary.simpleMessage(
            "注意：我們將在收到完整資料後,於15個工作日內完成審核和處理。感謝您的配合,如有任何疑問,請隨時聯繫客戶支持。"),
        "withdrawal": MessageLookupByLibrary.simpleMessage("提現"),
        "withdrawalFailed": MessageLookupByLibrary.simpleMessage("提現失敗，請稍後再試"),
        "withdrawalPoints": MessageLookupByLibrary.simpleMessage("提現積分: "),
        "withdrawalRecord": MessageLookupByLibrary.simpleMessage("提現記錄"),
        "year": MessageLookupByLibrary.simpleMessage("年")
      };
}
