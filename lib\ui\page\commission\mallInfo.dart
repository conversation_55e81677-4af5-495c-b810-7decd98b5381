import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:intl/intl.dart';

/// 兑换商品详情页面
class MallInfo extends StatefulWidget {
  const MallInfo({super.key, required this.mallId});

  final int mallId; //文章ID

  @override
  State<MallInfo> createState() => _MallInfoState();
}

class _MallInfoState extends State<MallInfo> {
  dynamic product = {}; //商品详情数据
  // String contextHtml = ''; //内容
  //获取服务表
  Future loadProduct() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Detail";
    var params = {};
    if (widget.mallId > 0) {
      params["id"] = widget.mallId.toString();
    }
    parameters["params"] = params;
    return HttpUtil.instance.post("/v1/product/query", parameters: parameters);
  }

  Future loadProductContent() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "GetLanguageData";
    var params = {};
    params["tableName"] = "product";
    params["dataId"] = widget.mallId.toString();
    parameters["params"] = params;
    return HttpUtil.instance.post("/v1/product/query", parameters: parameters);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).exchangeProduct),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: FutureBuilder(
        future: loadProduct(),
        builder: _buildFuture,
      ),
    );
  }

  Widget buildProductContent(BuildContext context, String contextHtml) {
    return Stack(
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMallBaseInfo(),
                const SizedBox(height: 8),
                _buildMallDetailInfo(context, contextHtml),
              ],
            ),
          ),
        ),
        _buildButtonOk(),
      ],
    );
  }

  //构造swiper
  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      if (snapshot.data["code"] == 200) {
        List list = snapshot.data["data"]["list"];
        var contextHtml = '';
        if (list.isNotEmpty) {
          contextHtml = list[0]['productDescLangText'] ?? '<div></div>';
          product = {
            'id': list[0]['id'],
            'name': list[0]['productNameLangValue'],
            'photoEn': list[0]['photoEn'] ?? "https://fakeimg.pl/80x80",
            'photoEs': list[0]['photoEs'] ?? "https://fakeimg.pl/80x80",
            'photoZh': list[0]['photoZh'] ?? "https://fakeimg.pl/80x80",
            'integral': list[0]['integral'] ?? 0,
            'productName': list[0]['productNameLangValue'] ?? '',
            'contextHtml': contextHtml,
          };
          return buildProductContent(context, contextHtml);
        }
      }
    } else {}

    return Center(child: Text(S.of(context).loading));
  }

  Widget _buildMallBaseInfo() {
    var localLang = Intl.getCurrentLocale();
    var lang = "photoEn";
    if (localLang == "zh_CN") {
      lang = "photoZh";
    } else if (localLang == "zh_HK") {
      lang = "photoEs";
    }
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          // 商品图片 4:3 比例
          AspectRatio(
            aspectRatio: 4 / 3,
            child: Image.network(
              product[lang],
              fit: BoxFit.cover,
            ),
          ),

          // 商品名称
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  product['productName'],
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ),

          // 积分显示
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  S.of(context).points,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                Transform.translate(
                  offset: const Offset(0, 4),
                  child: Text(
                    product['integral'].toString(),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF344C9C),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMallDetailInfo(BuildContext context, String contextHtml) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 40),
      margin: const EdgeInsets.only(bottom: 60),
      child: Column(
        children: [
          // 产品介绍标题
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const SizedBox(
                  width: 4,
                  height: 16,
                  child: ColoredBox(color: Color(0xFF344C9C)),
                ),
                const SizedBox(width: 8),
                Text(
                  S.of(context).productDesc,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          // 产品介绍内容
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Html(data: contextHtml),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonOk() {
    return Positioned(
      left: 40,
      right: 40,
      bottom: 40,
      child: SizedBox(
        width: 320,
        height: 40,
        child: ElevatedButton(
          onPressed: () {
            context.pushNamed(
              RouteNames.exchange,
              pathParameters: {'id': widget.mallId.toString()},
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF344C9C),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Text(
            S.of(context).exchange,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
