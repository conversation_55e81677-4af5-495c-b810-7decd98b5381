import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';

///
/// 输入提示框
///
class ShowInputAlertWidget extends StatefulWidget {
  const ShowInputAlertWidget(this.confirmCallback, this.title, this.placeholder,
      {super.key});

  final confirmCallback;
  final title;
  final placeholder;

  @override
  State<ShowInputAlertWidget> createState() => _ShowInputAlertWidgetState();
}

class _ShowInputAlertWidgetState extends State<ShowInputAlertWidget> {
  String inputValue = '';
  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text(widget.title),
      content: Column(
        children: [
          CupertinoTextField(
            placeholder: widget.placeholder,
            onChanged: (value) {
              inputValue = value;
            },
          )
        ],
      ),
      actions: [
        CupertinoDialogAction(
          child: Text(S.of(context).orderState3),
          onPressed: () {
            context.pop();
          },
        ),
        CupertinoDialogAction(
          child: Text(S.of(context).continute),
          onPressed: () {
            widget.confirmCallback(inputValue);
          },
        )
      ],
    );
  }
}
