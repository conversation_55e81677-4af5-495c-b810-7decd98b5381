import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';

class HexImageWidget extends StatelessWidget {
  final String base64String;

  const HexImageWidget({super.key, required this.base64String});

  String _cleanBase64String(String input) {
    // 如果字符串包含 data:image 前缀，移除它
    if (input.contains('data:image')) {
      return input.split(',')[1];
    }
    return input;
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (base64String.isEmpty) {
        debugPrint('Empty base64 string');
        return const Icon(Icons.broken_image);
      }

      // 清理 base64 字符串，移除 data:image 前缀
      String cleanedBase64 = _cleanBase64String(base64String);

      // 直接将base64字符串转换为字节数组
      Uint8List imageData = base64Decode(cleanedBase64);

      return Image.memory(
        imageData,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading image: $error');
          return Container(
            color: Colors.grey[200],
            child: const Icon(Icons.broken_image),
          );
        },
      );
    } catch (e) {
      return Container(
        color: Colors.grey[200],
        child: const Icon(Icons.broken_image),
      );
    }
  }
}
