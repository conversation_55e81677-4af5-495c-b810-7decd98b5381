import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/global.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:intl/intl.dart';
import 'package:new_solidcare/utils/shared_preferences_util.dart';

// 兑换确认页
class ExchangePage extends StatefulWidget {
  const ExchangePage({super.key, required this.mallId});
  final int mallId; //商品id
  @override
  State<ExchangePage> createState() => _ExchangePageState();
}

class _ExchangePageState extends State<ExchangePage> {
  final TextEditingController _quantityController =
      TextEditingController(text: '1');
  int quantity = 1;
  int availablePoints = 0;
  int customerId = 0;
  bool isSubmitting = false;

  getCurrentUser() {
    Global.getIntergral().then((data) {
      setState(() {
        availablePoints = data?.interal ?? 0;
      });
    });

    SharedPreferencesUtil.getInstance().getString("User").then((value) {
      setState(() {
        if (value != null) {
          var user = json.decode(value);
          customerId = user["id"];
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    getCurrentUser();
  }

  //获取服务表
  Future loadProduct() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Detail";
    var params = {};
    if (widget.mallId > 0) {
      params["id"] = widget.mallId.toString();
      params["lang"] = "zh";
    }
    parameters["params"] = params;
    return await HttpUtil.instance
        .post("/v1/product/query", parameters: parameters);
  }

  // // 商品相关数据，实际应该从API获取
  ProductModel product = ProductModel(
    id: 1,
    name: '',
    points: 0,
    imageUrl: '',
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: FutureBuilder(
        future: loadProduct(),
        builder: _buildBody,
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      toolbarHeight: 90,
      centerTitle: true,
      backgroundColor: Colors.transparent,
      title: Text(S.of(context).exchangeConfirm),
      flexibleSpace: FlexibleSpaceBar(
        background: Image.asset(
          'images/navBg.png', // 你的背景图路径
          fit: BoxFit.cover, // 使图片覆盖整个 AppBar
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      if (snapshot.data["code"] == 200) {
        List list = snapshot.data["data"]["list"];
        var item = list.first;
        var lang = "en";
        var localLang = Intl.getCurrentLocale();
        if (localLang == "zh_CN") {
          lang = "zh";
        } else if (localLang == "zh_HK") {
          lang = "es";
        }
        var imageUrl = "https://fakeimg.pl/80x80";
        var photoEn = item['photoEn'] ?? "https://fakeimg.pl/80x80";
        var photoZh = item['photoZh'] ?? "https://fakeimg.pl/80x80";
        var photoEs = item['photoEs'] ?? "https://fakeimg.pl/80x80";
        if (lang == "en") {
          imageUrl = photoEn;
        } else if (lang == "zh") {
          imageUrl = photoZh;
        } else if (lang == "es") {
          imageUrl = photoEs;
        }
        var integral = item['integral'];
        if (integral == null) {
          integral = 0;
        } else {
          integral = integral.toInt();
        }
        var myProduct = ProductModel(
          id: item['id'],
          name: item['productNameLangValue'] ?? "",
          points: integral,
          imageUrl: imageUrl,
        );
        product.points = myProduct.points;
        product.name = myProduct.name;
        product.imageUrl = myProduct.imageUrl;

        return Stack(children: [
          Column(
            children: [
              _buildDeliveryInfo(context),
              _buildAvailablePoints(),
              _buildProductInfo(),
            ],
          ),
          _buildSubmitButton(context),
        ]);
      } else {
        return const Center(child: CircularProgressIndicator());
      }
    } else {
      return const Center(child: CircularProgressIndicator());
    }
  }

  Widget _buildAvailablePoints() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(S.of(context).availablePoints),
          Transform.translate(
            offset: const Offset(0, 6),
            child: Text(
              availablePoints.toString(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF344C9C),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      color: const Color(0xFFFFF7E6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Icon(Icons.info_outline, color: Color(0xFFFF9800), size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${S.of(context).exchangeNotice}${S.of(context).storeAddress}',
              style: const TextStyle(color: Color(0xFFFF9800), fontSize: 14),
              maxLines: 6, // 设置最大行数为3
              overflow: TextOverflow.ellipsis, // 超出部分用省略号表示
            ),
          )
        ],
      ),
    );
  }

  Widget _buildProductInfo() {
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildProductHeader(),
          const SizedBox(height: 16),
          _buildQuantitySelector(),
          const SizedBox(height: 16),
          _buildTotalPoints(),
        ],
      ),
    );
  }

  Widget _buildProductHeader() {
    return SizedBox(
      height: 80,
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              product.imageUrl,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  product.name,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '${S.of(context).points}: ${product.points}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(S.of(context).quantity),
        Row(
          children: [
            _buildQuantityButton(
              icon: Icons.remove,
              onPressed: _decrementQuantity,
            ),
            SizedBox(
              width: 50,
              child: TextField(
                controller: _quantityController,
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  filled: true, // 启用填充
                  fillColor: Color.fromARGB(255, 238, 238, 238), // 设置背景颜色为灰色
                  contentPadding: EdgeInsets.zero,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)), // 设置圆角
                    borderSide: BorderSide.none, // 去掉边框线
                  ),
                ),
                onChanged: _onQuantityChanged,
              ),
            ),
            _buildQuantityButton(
              icon: Icons.add,
              onPressed: _incrementQuantity,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      constraints: const BoxConstraints(
        minWidth: 40,
        minHeight: 40,
      ),
    );
  }

  Widget _buildTotalPoints() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(S.of(context).total),
        Text(
          '${S.of(context).points}${_calculateTotalPoints()}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF344C9C),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BuildContext context) {
    return Positioned(
      left: 40,
      right: 40,
      bottom: 40,
      child: SizedBox(
        width: 320,
        height: 40,
        child: ElevatedButton(
          onPressed: _canSubmit() ? _submitOrder : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF344C9C),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Text(
            S.of(context).submitOrder,
            style: const TextStyle(fontSize: 16, color: Colors.white),
          ),
        ),
      ),
    );
  }

  // 业务逻辑方法
  void _decrementQuantity() {
    if (quantity > 1) {
      _quantityController.text = (quantity - 1).toString();
      setState(() {
        quantity--;
      });
    }
  }

  void _incrementQuantity() {
    _quantityController.text = (quantity + 1).toString();
    setState(() {
      quantity++;
    });
  }

  void _onQuantityChanged(String value) {
    final newQuantity = int.tryParse(value) ?? 1;
    if (newQuantity < 1) {
      _quantityController.text = '1';
    }
    setState(() {});
  }

  int _calculateTotalPoints() {
    return product.points * quantity;
  }

  bool _canSubmit() {
    if (isSubmitting) {
      return false;
    }
    if (availablePoints <= 0) {
      return false;
    }
    final totalPoints = _calculateTotalPoints();
    return totalPoints <= availablePoints;
  }

  void _submitOrder() async {
    setState(() {
      isSubmitting = true;
    });
    Map<String, dynamic> parameters = {};
    int point = _calculateTotalPoints();
    parameters["customerId"] = customerId.toString();
    parameters["integral"] = point.toString();
    parameters["productId"] = widget.mallId.toString();
    parameters["qty"] = quantity.toString();
    var result = await HttpUtil.instance
        .post("/v1/exchange/order/save", parameters: parameters);
    if (!mounted) return;
    setState(() {
      isSubmitting = false;
    });
    if (result["code"] == 200) {
      SharedPreferencesUtil.getInstance().getString("User").then((value) {
        var user = json.decode(value);
        int availablePoints = int.parse(user['integral'] ?? '0') - point;
        user['integral'] = availablePoints.toString();
        String userJson = json.encode(user);
        SharedPreferencesUtil.getInstance().setString("User", userJson);
        context.pushNamed(RouteNames.exchangeSuccess);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).exchangeFailed)),
      );
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }
}

// 数据模型
class ProductModel {
  int id;
  String name;
  int points;
  String imageUrl;

  ProductModel({
    required this.id,
    required this.name,
    required this.points,
    required this.imageUrl,
  });
}
