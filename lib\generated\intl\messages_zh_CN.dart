// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_CN locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_CN';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("关于我们"),
        "account": MessageLookupByLibrary.simpleMessage("账号: "),
        "address": MessageLookupByLibrary.simpleMessage("地址"),
        "amount": MessageLookupByLibrary.simpleMessage("金额"),
        "amountInvalid": MessageLookupByLibrary.simpleMessage("請輸入正確的金額"),
        "amountRequired": MessageLookupByLibrary.simpleMessage("请输入金额"),
        "apointAlert": MessageLookupByLibrary.simpleMessage("预约成功"),
        "apointOrderDate": MessageLookupByLibrary.simpleMessage("可预约日期"),
        "apointOrderTime": MessageLookupByLibrary.simpleMessage("可预约时间"),
        "apointTime": MessageLookupByLibrary.simpleMessage("预约时间"),
        "apointment": MessageLookupByLibrary.simpleMessage("预约"),
        "applying": MessageLookupByLibrary.simpleMessage("申请中"),
        "area": MessageLookupByLibrary.simpleMessage("平方英尺"),
        "availablePoints": MessageLookupByLibrary.simpleMessage("可用积分："),
        "awardRule": MessageLookupByLibrary.simpleMessage("奖励规则"),
        "backToHome": MessageLookupByLibrary.simpleMessage("返回首页"),
        "blok": MessageLookupByLibrary.simpleMessage("博客"),
        "cancelled": MessageLookupByLibrary.simpleMessage("作废"),
        "cannotOpenNewWindow": MessageLookupByLibrary.simpleMessage("无法打开新窗口"),
        "cashAmount": MessageLookupByLibrary.simpleMessage("折现金额: "),
        "category": MessageLookupByLibrary.simpleMessage("分类"),
        "changePwd": MessageLookupByLibrary.simpleMessage("修改密码"),
        "changePwdHolder": MessageLookupByLibrary.simpleMessage("请输入新密码"),
        "city": MessageLookupByLibrary.simpleMessage("城市"),
        "commission": MessageLookupByLibrary.simpleMessage("分享赚佣金"),
        "commissionTitle": MessageLookupByLibrary.simpleMessage("分享赚佣金"),
        "commodity": MessageLookupByLibrary.simpleMessage("商品"),
        "contact": MessageLookupByLibrary.simpleMessage("联系我们"),
        "contactAlert": MessageLookupByLibrary.simpleMessage("請輸入郵箱和密码"),
        "contactAlert2": MessageLookupByLibrary.simpleMessage("提交成功"),
        "contactAlert3": MessageLookupByLibrary.simpleMessage("請輸入郵箱"),
        "contactAlert4": MessageLookupByLibrary.simpleMessage("您的密码已重置,请查看邮箱"),
        "contactAlert5": MessageLookupByLibrary.simpleMessage("您的密码已经修改"),
        "contactTye": MessageLookupByLibrary.simpleMessage("联系方式"),
        "continute": MessageLookupByLibrary.simpleMessage("确定"),
        "customerService": MessageLookupByLibrary.simpleMessage("拨打客服电话"),
        "date": MessageLookupByLibrary.simpleMessage("日期："),
        "designation": MessageLookupByLibrary.simpleMessage("名称："),
        "discountAmount": MessageLookupByLibrary.simpleMessage("折现金额"),
        "douying": MessageLookupByLibrary.simpleMessage("抖音"),
        "downloadStarted": MessageLookupByLibrary.simpleMessage("图片开始下载"),
        "earnCommission": MessageLookupByLibrary.simpleMessage("分享赚佣金"),
        "email": MessageLookupByLibrary.simpleMessage("邮箱"),
        "emailRequired": MessageLookupByLibrary.simpleMessage("请输入邮箱"),
        "exchange": MessageLookupByLibrary.simpleMessage("兑换"),
        "exchangeConfirm": MessageLookupByLibrary.simpleMessage("兑换确认"),
        "exchangeFailed": MessageLookupByLibrary.simpleMessage("兑换失败，请稍后再试"),
        "exchangeMall": MessageLookupByLibrary.simpleMessage("兑换商城"),
        "exchangeNotice": MessageLookupByLibrary.simpleMessage(
            "成功兑换后，请到Solidcare Home Improvements 公司门店领取您的礼品或服务。"),
        "exchangePoints": MessageLookupByLibrary.simpleMessage("兑换积分"),
        "exchangeProduct": MessageLookupByLibrary.simpleMessage("兑换商品"),
        "exchangeRecord": MessageLookupByLibrary.simpleMessage("兑换记录"),
        "exchangeSuccess": MessageLookupByLibrary.simpleMessage("兑换成功"),
        "exchangeablePoints": MessageLookupByLibrary.simpleMessage("可兑换积分"),
        "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
        "firstName": MessageLookupByLibrary.simpleMessage("名"),
        "forgetpwd": MessageLookupByLibrary.simpleMessage("忘记密码"),
        "friendsCount": MessageLookupByLibrary.simpleMessage("好友数: "),
        "friendsList": MessageLookupByLibrary.simpleMessage("好友列表"),
        "giftExchange": MessageLookupByLibrary.simpleMessage("礼品兑换"),
        "goApointment": MessageLookupByLibrary.simpleMessage("前往预约"),
        "heat": MessageLookupByLibrary.simpleMessage("热度"),
        "hoemShareTitle": MessageLookupByLibrary.simpleMessage("分享赚佣金"),
        "home": MessageLookupByLibrary.simpleMessage("首页"),
        "houseArea": MessageLookupByLibrary.simpleMessage("房子的大概面积"),
        "houseYear": MessageLookupByLibrary.simpleMessage("房子的大概房龄"),
        "imageSavedToGallery": MessageLookupByLibrary.simpleMessage("图片已保存到相册"),
        "invalidEmail": MessageLookupByLibrary.simpleMessage("请输入正确的邮箱"),
        "invalidMobile": MessageLookupByLibrary.simpleMessage("请输入正确的手机号"),
        "lastName": MessageLookupByLibrary.simpleMessage("姓"),
        "leanmore": MessageLookupByLibrary.simpleMessage("了解详情"),
        "loading": MessageLookupByLibrary.simpleMessage("加载中"),
        "login": MessageLookupByLibrary.simpleMessage("登      录"),
        "loginAlert": MessageLookupByLibrary.simpleMessage("请先登录"),
        "loginAlert2": MessageLookupByLibrary.simpleMessage("请输入登录名与密码"),
        "loginMobile": MessageLookupByLibrary.simpleMessage("邮箱"),
        "loginName": MessageLookupByLibrary.simpleMessage("用户名"),
        "loginPassword": MessageLookupByLibrary.simpleMessage("密码"),
        "loginTitle": MessageLookupByLibrary.simpleMessage("登录以获取最佳体验"),
        "loginTitle2": MessageLookupByLibrary.simpleMessage("已有账号,请登录?"),
        "logout": MessageLookupByLibrary.simpleMessage("登出"),
        "mallInfo": MessageLookupByLibrary.simpleMessage("商城信息"),
        "mobile": MessageLookupByLibrary.simpleMessage("移动电话"),
        "mobileRequired": MessageLookupByLibrary.simpleMessage("请输入手机号"),
        "morepreferential": MessageLookupByLibrary.simpleMessage("如何获取更多优惠?"),
        "name": MessageLookupByLibrary.simpleMessage("姓名"),
        "nameRequired": MessageLookupByLibrary.simpleMessage("请输入姓名"),
        "needStoragePermission":
            MessageLookupByLibrary.simpleMessage("需要存储权限来保存图片"),
        "noImageToSave": MessageLookupByLibrary.simpleMessage("没有可保存的图片"),
        "noMoreData": MessageLookupByLibrary.simpleMessage("没有更多数据了"),
        "notLoggedIn": MessageLookupByLibrary.simpleMessage("未登录"),
        "order": MessageLookupByLibrary.simpleMessage("订单"),
        "orderDate": MessageLookupByLibrary.simpleMessage("预约日期"),
        "orderNumber": MessageLookupByLibrary.simpleMessage("订单号: "),
        "orderState0": MessageLookupByLibrary.simpleMessage("未提交"),
        "orderState1": MessageLookupByLibrary.simpleMessage("已提交"),
        "orderState2": MessageLookupByLibrary.simpleMessage("已完成"),
        "orderState3": MessageLookupByLibrary.simpleMessage("已取消"),
        "orderState4": MessageLookupByLibrary.simpleMessage("已确认"),
        "orderTime": MessageLookupByLibrary.simpleMessage("预约时间"),
        "orderTitle": MessageLookupByLibrary.simpleMessage("订单列表"),
        "paid": MessageLookupByLibrary.simpleMessage("已支付"),
        "paymentTime": MessageLookupByLibrary.simpleMessage("支付时间: "),
        "permissionClear": MessageLookupByLibrary.simpleMessage("取消"),
        "permissionDesc":
            MessageLookupByLibrary.simpleMessage("需要存储权限以保存文件，是否去设置页面开启权限？"),
        "permissionReject": MessageLookupByLibrary.simpleMessage("权限被拒绝"),
        "permissionSetting": MessageLookupByLibrary.simpleMessage("去设置"),
        "personal": MessageLookupByLibrary.simpleMessage("我的"),
        "phone": MessageLookupByLibrary.simpleMessage("家庭电话"),
        "points": MessageLookupByLibrary.simpleMessage("积分："),
        "pointsDetail": MessageLookupByLibrary.simpleMessage("积分明细"),
        "pointsDetails": MessageLookupByLibrary.simpleMessage("积分明细"),
        "postCode": MessageLookupByLibrary.simpleMessage("邮政编号"),
        "preferential": MessageLookupByLibrary.simpleMessage("优惠信息"),
        "productDesc": MessageLookupByLibrary.simpleMessage("产品简介"),
        "productTitle": MessageLookupByLibrary.simpleMessage("产品选项"),
        "promotionDesc1": MessageLookupByLibrary.simpleMessage(
            "立即下载我们的App并注册成为会员，不仅可以享受高达5%的现金返还，还能额外累积10%的积分，"),
        "promotionDesc2":
            MessageLookupByLibrary.simpleMessage("用于未来的兑换或折扣。将此海报保存并分享给您的好友，"),
        "promotionDesc3": MessageLookupByLibrary.simpleMessage(
            "邀请他们一起加入，共同体验家居改善的便捷与乐趣，享受实实在在的优惠！"),
        "promotionTitle": MessageLookupByLibrary.simpleMessage(
            "Solidcare Home Improvements 现推出双重优惠！"),
        "province": MessageLookupByLibrary.simpleMessage("省份"),
        "quantity": MessageLookupByLibrary.simpleMessage("数量："),
        "redbook": MessageLookupByLibrary.simpleMessage("小红书"),
        "redemptionHistory": MessageLookupByLibrary.simpleMessage("兑换记录"),
        "regCode": MessageLookupByLibrary.simpleMessage("邀请码"),
        "regContent":
            MessageLookupByLibrary.simpleMessage("注册一个新的账号, 您需要同意相关条款"),
        "regPasword": MessageLookupByLibrary.simpleMessage("创建密码"),
        "regSuccess": MessageLookupByLibrary.simpleMessage("注册成功"),
        "regTitle": MessageLookupByLibrary.simpleMessage("注册一个新的账号?"),
        "register": MessageLookupByLibrary.simpleMessage("创建会员"),
        "remarkTitle": MessageLookupByLibrary.simpleMessage("填写房子的保温情况"),
        "remarks": MessageLookupByLibrary.simpleMessage("备注："),
        "reminderRecord": MessageLookupByLibrary.simpleMessage("提醒记录"),
        "reviewed": MessageLookupByLibrary.simpleMessage("已审核"),
        "rewardRules": MessageLookupByLibrary.simpleMessage("奖励规则"),
        "saveFailed": MessageLookupByLibrary.simpleMessage("保存失败"),
        "saveImage": MessageLookupByLibrary.simpleMessage("保存图片"),
        "saveInNewWindow": MessageLookupByLibrary.simpleMessage("请在新窗口中右键保存图片"),
        "scanMe": MessageLookupByLibrary.simpleMessage("扫一扫我"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "selectLang": MessageLookupByLibrary.simpleMessage("语言选择"),
        "selfTitle": MessageLookupByLibrary.simpleMessage("会员中心"),
        "service": MessageLookupByLibrary.simpleMessage("服务"),
        "serviceTitle": MessageLookupByLibrary.simpleMessage("服务选项"),
        "servicearea": MessageLookupByLibrary.simpleMessage("服务区域"),
        "settled": MessageLookupByLibrary.simpleMessage("已结算"),
        "shareEarn": MessageLookupByLibrary.simpleMessage("分享拿佣金"),
        "shareEarnPoints": MessageLookupByLibrary.simpleMessage("分享赚积分"),
        "shareLink": MessageLookupByLibrary.simpleMessage("分享邀请"),
        "sharePlaceHolder": MessageLookupByLibrary.simpleMessage("请输入分享邮箱地址"),
        "shareSuccess": MessageLookupByLibrary.simpleMessage("已发邀请"),
        "shareTitle": MessageLookupByLibrary.simpleMessage("分享海报"),
        "shared": MessageLookupByLibrary.simpleMessage("已分享"),
        "shipped": MessageLookupByLibrary.simpleMessage("已发货"),
        "showPassword": MessageLookupByLibrary.simpleMessage("显示密码"),
        "status": MessageLookupByLibrary.simpleMessage("状态："),
        "storeAddress": MessageLookupByLibrary.simpleMessage(
            "地址：Address:16 Esna Park Dr #6, Markham, ON L3R 5X1，Phone: ************"),
        "submit": MessageLookupByLibrary.simpleMessage("立即提交"),
        "submitOrder": MessageLookupByLibrary.simpleMessage("提交订单"),
        "submitTime": MessageLookupByLibrary.simpleMessage("提交时间: "),
        "total": MessageLookupByLibrary.simpleMessage("合计"),
        "totalPoints": MessageLookupByLibrary.simpleMessage("累计积分"),
        "unit": MessageLookupByLibrary.simpleMessage("单元"),
        "unlogin": MessageLookupByLibrary.simpleMessage("未登录"),
        "unsettled": MessageLookupByLibrary.simpleMessage("未结算"),
        "user": MessageLookupByLibrary.simpleMessage("用户："),
        "withdrawCash": MessageLookupByLibrary.simpleMessage("提现"),
        "withdrawFailed": MessageLookupByLibrary.simpleMessage("提现失败，请稍后再试"),
        "withdrawRecord": MessageLookupByLibrary.simpleMessage("提现记录"),
        "withdrawSuccess": MessageLookupByLibrary.simpleMessage("提现成功"),
        "withdrawWarning": MessageLookupByLibrary.simpleMessage(
            "注：我们将在收到完整资料后,于15个工作日内完成审核和处理。感谢您的配合,如有任何疑问,请随时联系客户支持。"),
        "withdrawal": MessageLookupByLibrary.simpleMessage("提现"),
        "withdrawalFailed": MessageLookupByLibrary.simpleMessage("提现失败，请稍后再试"),
        "withdrawalPoints": MessageLookupByLibrary.simpleMessage("提现积分: "),
        "withdrawalRecord": MessageLookupByLibrary.simpleMessage("提现记录"),
        "year": MessageLookupByLibrary.simpleMessage("年")
      };
}
