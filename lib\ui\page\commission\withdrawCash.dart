import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/global.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:new_solidcare/utils/shared_preferences_util.dart';

/// 提现页面
class WithdrawCashPage extends StatefulWidget {
  const WithdrawCashPage({super.key});

  @override
  State<WithdrawCashPage> createState() => _WithdrawCashPageState();
}

class _WithdrawCashPageState extends State<WithdrawCashPage> {
  final _formKey = GlobalKey<FormState>();
  final _withdrawFormData = WithdrawFormData();
  final TextEditingController _pointsController =
      TextEditingController(text: '0');
  // 用户积分信息，实际应该从API获取
  final UserPointsInfo userPoints = UserPointsInfo(
    availablePoints: 0,
    exchangePoints: 0,
    discountAmount: 0,
    customerId: 0,
  );

  bool _isSubmitting = false;
  getCurrentUser() {
    Global.getIntergral().then((data) {
      setState(() {
        userPoints.availablePoints = data?.interal ?? 0;
        _pointsController.text = userPoints.availablePoints.toString();
        userPoints.exchangePoints = userPoints.availablePoints;
        userPoints.discountAmount = (userPoints.exchangePoints / 2).round();
      });
    });
    SharedPreferencesUtil.getInstance().getString("User").then((value) {
      setState(() {
        if (value != null) {
          var user = json.decode(value);
          userPoints.customerId = user["id"];
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    getCurrentUser();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      centerTitle: true,
      toolbarHeight: 90,
      title: Text(S.of(context).withdrawCash),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Image.asset(
          'images/navBg.png', // 你的背景图路径
          fit: BoxFit.cover, // 使图片覆盖整个 AppBar
        ),
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildWarningMessage(),
          _buildPointsInfo(),
          _buildWithdrawForm(),
        ],
      ),
    );
  }

  Widget _buildWarningMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      color: const Color(0xFFFFF7E6),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Color(0xFFFF9800), size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              S.of(context).withdrawWarning,
              style: const TextStyle(color: Color(0xFFFF9800), fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsInfo() {
    return Container(
      padding: const EdgeInsets.only(top: 12, left: 16, right: 16),
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                S.of(context).availablePoints,
                style: TextStyle(color: Colors.grey[700]),
              ),
              Text(
                userPoints.availablePoints.toString(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF344C9C),
                ),
              ),
            ],
          ),
          _buildExchangeInfo(),
        ],
      ),
    );
  }

  Widget _buildExchangeInfo() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          const SizedBox(height: 12),
          Container(
            height: 30,
            margin: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${S.of(context).discountAmount}: ",
                  style: TextStyle(color: Colors.grey[700]),
                ),
                Text(
                  'C\$${userPoints.discountAmount}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 30,
            margin: const EdgeInsets.symmetric(vertical: 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "${S.of(context).exchangePoints}: ",
                  style: TextStyle(color: Colors.grey[700]),
                ),
                SizedBox(
                  width: 120,
                  height: 30,
                  child: TextField(
                    controller: _pointsController,
                    textAlign: TextAlign.left,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      userPoints.exchangePoints = int.parse(value);
                      if (userPoints.exchangePoints >
                          userPoints.availablePoints) {
                        userPoints.exchangePoints = userPoints.availablePoints;
                        _pointsController.text =
                            userPoints.availablePoints.toString();
                        userPoints.discountAmount =
                            (userPoints.exchangePoints / 2).round();
                      }
                      setState(() {
                        userPoints.exchangePoints = int.parse(value);
                        userPoints.discountAmount =
                            (userPoints.exchangePoints / 2).round();
                      });
                    },
                    decoration: const InputDecoration(
                      filled: true, // 启用填充
                      fillColor:
                          Color.fromARGB(255, 238, 238, 238), // 设置背景颜色为灰色
                      contentPadding: EdgeInsets.zero,
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.all(Radius.circular(8)), // 设置圆角
                        borderSide: BorderSide.none, // 去掉边框线
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildWithdrawForm() {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.only(top: 8),
      color: Colors.white,
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            _buildFormField(
              id: 1,
              label: S.of(context).name,
              onSaved: (value) => _withdrawFormData.name = value ?? '',
              validator: Validators.required(S.of(context).nameRequired),
            ),
            _buildFormField(
              id: 2,
              label: S.of(context).mobile,
              keyboardType: TextInputType.text,
              onSaved: (value) => _withdrawFormData.mobile = value ?? '',
              validator: Validators.compose([
                Validators.required(S.of(context).mobileRequired),
                Validators.phone(S.of(context).invalidMobile),
              ]),
            ),
            _buildFormField(
              id: 3,
              label: S.of(context).email,
              keyboardType: TextInputType.emailAddress,
              onSaved: (value) => _withdrawFormData.email = value ?? '',
              validator: Validators.compose([
                Validators.required(S.of(context).emailRequired),
                Validators.email(S.of(context).invalidEmail),
              ]),
            ),
            const SizedBox(height: 24),
            _buildSubmitButton(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required int id,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String?)? onSaved,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        keyboardType: keyboardType,
        validator: validator,
        onSaved: onSaved,
        onChanged: (value) {
          setState(() {
            switch (id) {
              case 1:
                _withdrawFormData.name = value;
              case 2:
                _withdrawFormData.mobile = value;
              case 3:
                _withdrawFormData.email = value;
            }
          });
        },
        decoration: InputDecoration(
          labelText: label,
          filled: true,
          fillColor: Colors.grey[100],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: 320,
      height: 40,
      child: ElevatedButton(
        // onPressed: _submitForm,
        onPressed: _canSubmit() ? _submitForm : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF344C9C),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Text(
          S.of(context).submit,
          style: const TextStyle(fontSize: 16, color: Colors.white),
        ),
      ),
    );
  }

  bool _canSubmit() {
    if (_isSubmitting) {
      return false;
    }
    return _pointsController.text.isNotEmpty &&
        _withdrawFormData.name.isNotEmpty &&
        _withdrawFormData.mobile.isNotEmpty &&
        _withdrawFormData.email.isNotEmpty;
  }

  void _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isSubmitting = true;
      });
      _formKey.currentState?.save();
      Map<String, dynamic> parameters = {};
      parameters["customerId"] = userPoints.customerId.toString();
      parameters["integral"] = userPoints.exchangePoints.toString();
      parameters["email"] = _withdrawFormData.email.toString();
      parameters["name"] = _withdrawFormData.name.toString();
      parameters["mobile"] = _withdrawFormData.mobile.toString();
      var result = await HttpUtil.instance
          .post("/v1/withdrawal/order/save", parameters: parameters);
      if (!mounted) return;
      setState(() {
        _isSubmitting = false;
      });
      if (result["code"] == 200) {
        SharedPreferencesUtil.getInstance().getString("User").then((value) {
          var user = json.decode(value);
          int availablePoints =
              int.parse(user['integral'] ?? '0') - userPoints.exchangePoints;
          user['integral'] = availablePoints.toString();
          String userJson = json.encode(user);
          SharedPreferencesUtil.getInstance().setString("User", userJson);
          context.pushNamed(RouteNames.withdrawCashSuccess);
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).withdrawFailed)),
        );
      }
    }
  }

  @override
  void dispose() {
    _withdrawFormData.dispose();
    super.dispose();
  }
}

// 数据模型
class UserPointsInfo {
  int availablePoints;
  int exchangePoints;
  int discountAmount;
  int customerId;

  UserPointsInfo({
    this.availablePoints = 0,
    this.exchangePoints = 0,
    this.discountAmount = 0,
    this.customerId = 0,
  });
}

class WithdrawFormData {
  String name = '';
  String mobile = '';
  String email = '';

  void dispose() {
    // 清理资源
  }
}

// 表单验证器
class Validators {
  static String? Function(String?) required(String message) {
    return (value) {
      if (value == null || value.isEmpty) {
        return message;
      }
      return null;
    };
  }

  static String? Function(String?) email(String message) {
    return (value) {
      if (value == null ||
          !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
        return message;
      }
      return null;
    };
  }

  static String? Function(String?) phone(String message) {
    return (value) {
      if (value == null || value.length < 2 || value.length > 30) {
        return message;
      }
      return null;
    };
  }

  static String? Function(String?) compose(
      List<String? Function(String?)> validators) {
    return (value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
