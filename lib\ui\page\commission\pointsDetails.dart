import 'package:flutter/material.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/utils/http_util.dart';

/// 积分记录，佣金记录
class PointsDetails extends StatefulWidget {
  const PointsDetails({super.key});

  @override
  State<PointsDetails> createState() => _PointsDetailsState();
}

class _PointsDetailsState extends State<PointsDetails>
    with SingleTickerProviderStateMixin {
  int pageIndex = 1;
  bool isLoading = false;
  bool hasMore = true;
  List pointsList = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 添加滚动监听
    _scrollController.addListener(_onScroll);
    // 初始加载数据
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    if (currentScroll >= (maxScroll - 200) && !isLoading && hasMore) {
      _loadMore();
    }
  }

  Future<void> _loadMore() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
      pageIndex++;
    });

    await _loadData();
  }

  Future<void> _loadData() async {
    try {
      Map<String, dynamic> parameters = {
        "query": "OrderList",
        "page": pageIndex,
        "limit": 10,
      };

      final result = await HttpUtil.instance
          .post("/v1/integral/order/query", parameters: parameters);

      if (result != null && result["code"] == 200) {
        List newList = result["data"]["list"] ?? [];
        int total = result["data"]["total"] ?? 0;

        setState(() {
          if (pageIndex == 1) {
            pointsList = newList;
          } else {
            pointsList.addAll(newList);
          }

          hasMore = pointsList.length < total;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      debugPrint('Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    //获取产品信息
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).pointsDetail),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Color(0xfff5f5f5),
        ),
        child: ListView(
          controller: _scrollController,
          children: [
            getPointsList(pointsList),
            if (isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(),
                ),
              ),
            if (!hasMore && pointsList.isNotEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(S.of(context).noMoreData),
                ),
              ),
            // 添加底部空间
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData && snapshot.data["code"] == 200) {
      List list = snapshot.data["data"]["list"];
      return SingleChildScrollView(
        child: Column(
          children: [
            getPointsList(list),
          ],
        ),
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  Widget getPointsList(List pointsData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      child: Column(
        children: pointsData.asMap().entries.map((entry) {
          // 获取当前元素并设置默认值
          var point = entry.value;
          var orderNo = point['orderNo'] ?? '';
          var createdTime = point['createdTime'] ?? '';
          var customerName = point['customerName'] ?? '';
          var remarks = point['remarks'] ?? '';
          var integral = point['integral'] ?? 0;
          var state = point['state'] ?? 0;

          // 根据状态选择颜色
          var color = 'red';
          var textColor = Colors.red;
          var statueText = S.of(context).unsettled;
          if (state == 0) {
            color = 'green';
            textColor = Colors.green;
            statueText = S.of(context).unsettled;
          } else if (state == 1) {
            color = 'red';
            textColor = Colors.red;
            statueText = S.of(context).settled;
          } else if (state == 2) {
            color = 'red';
            textColor = Colors.grey;
            statueText = S.of(context).cancelled;
          }

          return Container(
            margin: const EdgeInsets.only(bottom: 8.0),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  'images/point_$color.png',
                  width: 20,
                  height: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(context).orderNumber}$orderNo',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        '${S.of(context).date}$createdTime',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        '${S.of(context).user}$customerName',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        '${S.of(context).remarks}$remarks',
                        style: TextStyle(color: Colors.grey[500]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Text(
                      integral.toString(),
                      style: const TextStyle(fontSize: 20),
                    ),
                    Text(
                      statueText,
                      style: TextStyle(fontSize: 20, color: textColor),
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
