// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

class AppColors {
  static const Color COLOR_333333 = Color(0xff333333);
  static const Color COLOR_FFFFFF = Color(0xffffffff);
  static const Color COLOR_FF5722 = Color(0xffff5E4C);
  static const Color COLOR_999999 = Color(0xff999999);
  static const Color COLOR_666666 = Color(0xff666666);
  static const Color COLOR_DBDBDB = Color(0xffdbdbdb);
  static const Color COLOR_F0F0F0 = Color(0xfff0f0f0);
  static const Color COLOR_F6F6F6 = Color(0xfff6f6f6);
  static const Color COLOR_F8F8F8 = Color(0xfff8f8f8);
  static const Color COLOR_F9F9F9 = Color(0xfff9f9f9);
  static const Color COLOR_EFF21 = Color(0xFF7eff21);
  static const Color COLOR_DCDCDC = Color(0xFFDCDCDC);
  static const Color COLOR_E99D42 = Color(0xFFE99D42);
  static const Color COLOR_EAEAEA = Color(0xFFEAEAEA);
  static const Color COLOR_PRIMARY = Color(0xFF29408F);
  static const Color COLOR_TRANSPARENT = Colors.transparent;
  static const MaterialColor themeColor = MaterialColor(
    0xffff5722,
    <int, Color>{
      50: Color(0xffff5722),
      100: Color(0xffff5722),
      200: Color(0xffff5722),
      300: Color(0xffff5722),
      400: Color(0xffff5722),
      500: Color(0xffff5722),
      600: Color(0xffff5722),
      700: Color(0xffff5722),
      800: Color(0xffff5722),
      900: Color(0xffff5722),
    },
  );
}
