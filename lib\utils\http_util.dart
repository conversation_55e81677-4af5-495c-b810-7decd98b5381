import 'package:dio/dio.dart';
import 'package:new_solidcare/utils/language_util.dart';
import 'shared_preferences_util.dart';
import 'package:intl/intl.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';

class HttpUtil {
  // 工厂模式
  static final HttpUtil _httpUtil = HttpUtil();
  static HttpUtil get instance => _getInstance();
  static String lang = "en";
  var dio;

  static HttpUtil _getInstance() {
    return _httpUtil;
  }

  HttpUtil() {
    // const String apiUrl = 'http://bwm.leazy.cn:30000';
    // const String apiUrl = 'http://solidcareapi.api2.hotgz.com/';
    const String apiUrl = 'http://solidcareapi.us.luckycrm.com';

    BaseOptions options = BaseOptions(
        baseUrl: apiUrl,
        connectTimeout: 15000,
        receiveTimeout: 15000,
        contentType: "application/json");
    dio = Dio(options);
    //获取缓存token
    String token = "";
    var cookieJar = CookieJar();
    dio.interceptors.add(CookieManager(cookieJar));

    dio.interceptors
        .add(InterceptorsWrapper(onRequest: (options, handler) async {
      await SharedPreferencesUtil.getInstance()
          .getString("Token")
          .then((value) {
        if (value != null) {
          options.headers['Token'] = value;
        }
      });
      await SharedPreferencesUtil.getInstance()
          .getString("SessionId")
          .then((value) {
        if (value != null) {
          options.headers['SessionId'] = value;
        }
      });
      return handler.next(options); //continue
    }, onResponse: (response, handler) {
      // Do something with response data
      if (response.headers.map.containsKey("Token")) {
        String resToken = response.headers.value("Token") ?? "";
        if (resToken != "") {
          SharedPreferencesUtil.getInstance().setString("Token", resToken);
        }
      }
      if (response.headers.map.containsKey("SessionId")) {
        String SessionId = response.headers.value("SessionId") ?? "";
        SharedPreferencesUtil.getInstance().setString("SessionId", SessionId);
      }
      return handler.next(response); // continue
    }, onError: (DioError e, handler) {
      // Do something with response error
      return handler.next(e); //continue
    }));
  }

  //get请求
  Future get(String url,
      {Map<String, dynamic>? parameters,
      Options? options,
      CancelToken? cancelToken}) async {
    Response response;
    parameters ??= <String, dynamic>{};

    if (url != "/v1/qrcode/get") {
      String lang = await LanguageUtil.getSavedLanguage() ?? "en";
      parameters["lang"] = lang;
    }
    try {
      if (options != null) {
        response = await dio.get(
          url,
          queryParameters: parameters,
          options: options,
          cancelToken: cancelToken,
        );
      } else {
        response = await dio.get(
          url,
          queryParameters: parameters,
          cancelToken: cancelToken,
        );
      }
      return response.data;
    } on DioError catch (error) {
      // 请求错误处理
      if (error.response != null) {
        if (300 == error.response!.statusCode ||
            304 == error.response!.statusCode ||
            305 == error.response!.statusCode) {
          //token过期，需要重新登录
          SharedPreferencesUtil.getInstance().clear();
        }
      }
      return {"code": 305};
    }
  }

  //post请求
  Future post(String url,
      {Map<String, dynamic>? parameters, Options? options}) async {
    Response response;
    parameters ??= <String, dynamic>{};
    Map params = {};
    if (parameters.containsKey("params")) {
      params = parameters["params"];
    }
    String lang = await LanguageUtil.getSavedLanguage() ?? "en";
    params["lang"] = lang;
    parameters["lang"] = lang;
    parameters["params"] = params;
    try {
      if (options != null) {
        response = await dio.post(url, data: parameters, options: options);
      } else {
        response = await dio.post(url, data: parameters);
      }
      return response.data;
    } on DioError catch (error) {
      // 请求错误处理
      if (error.response != null) {
        if (300 == error.response!.statusCode ||
            304 == error.response!.statusCode ||
            305 == error.response!.statusCode) {
          //token过期，需要重新登录
          SharedPreferencesUtil.getInstance().clear();
        }
      }
      return {"code": 305};
    }
  }
}
