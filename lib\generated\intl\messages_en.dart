// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("About"),
        "account": MessageLookupByLibrary.simpleMessage("Account: "),
        "address": MessageLookupByLibrary.simpleMessage("Address"),
        "amount": MessageLookupByLibrary.simpleMessage("Amount"),
        "amountInvalid":
            MessageLookupByLibrary.simpleMessage("Please input correct amount"),
        "amountRequired":
            MessageLookupByLibrary.simpleMessage("Please input amount"),
        "apointAlert":
            MessageLookupByLibrary.simpleMessage("Apointment success"),
        "apointOrderDate": MessageLookupByLibrary.simpleMessage("Order date"),
        "apointOrderTime": MessageLookupByLibrary.simpleMessage("Order time"),
        "apointTime": MessageLookupByLibrary.simpleMessage("Apointment time"),
        "apointment": MessageLookupByLibrary.simpleMessage("Appointment"),
        "applying": MessageLookupByLibrary.simpleMessage("Applying"),
        "area": MessageLookupByLibrary.simpleMessage("Square foot"),
        "availablePoints":
            MessageLookupByLibrary.simpleMessage("Available Points: "),
        "awardRule": MessageLookupByLibrary.simpleMessage("Reward Rules"),
        "backToHome": MessageLookupByLibrary.simpleMessage("Back to Home"),
        "blok": MessageLookupByLibrary.simpleMessage("Blog"),
        "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "cannotOpenNewWindow":
            MessageLookupByLibrary.simpleMessage("Cannot open new window"),
        "cashAmount": MessageLookupByLibrary.simpleMessage("Cash Amount: "),
        "category": MessageLookupByLibrary.simpleMessage("Category"),
        "changePwd": MessageLookupByLibrary.simpleMessage("Change password"),
        "changePwdHolder":
            MessageLookupByLibrary.simpleMessage("Please enter new password"),
        "city": MessageLookupByLibrary.simpleMessage("City"),
        "commission": MessageLookupByLibrary.simpleMessage("Bouns"),
        "commissionTitle":
            MessageLookupByLibrary.simpleMessage("Share & Earn Commission"),
        "commodity": MessageLookupByLibrary.simpleMessage("Commodity"),
        "contact": MessageLookupByLibrary.simpleMessage("Contact Us"),
        "contactAlert":
            MessageLookupByLibrary.simpleMessage("Please input name and email"),
        "contactAlert2": MessageLookupByLibrary.simpleMessage("Submit success"),
        "contactAlert3":
            MessageLookupByLibrary.simpleMessage("Please input email"),
        "contactAlert4": MessageLookupByLibrary.simpleMessage(
            "Your password has reset,Please look your mailbox"),
        "contactAlert5":
            MessageLookupByLibrary.simpleMessage("Your password has changed"),
        "contactTye": MessageLookupByLibrary.simpleMessage("Contact Info"),
        "continute": MessageLookupByLibrary.simpleMessage("Continue"),
        "customerService":
            MessageLookupByLibrary.simpleMessage("Customer Service"),
        "date": MessageLookupByLibrary.simpleMessage("Date: "),
        "designation": MessageLookupByLibrary.simpleMessage("Designation:"),
        "discountAmount":
            MessageLookupByLibrary.simpleMessage("Discount Amount"),
        "douying": MessageLookupByLibrary.simpleMessage("Tik Tok"),
        "downloadStarted":
            MessageLookupByLibrary.simpleMessage("Image download started"),
        "earnCommission":
            MessageLookupByLibrary.simpleMessage("Earn Commission"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "emailRequired":
            MessageLookupByLibrary.simpleMessage("Please input email"),
        "exchange": MessageLookupByLibrary.simpleMessage("Exchange"),
        "exchangeConfirm":
            MessageLookupByLibrary.simpleMessage("Exchange Confirmation"),
        "exchangeFailed": MessageLookupByLibrary.simpleMessage(
            "Exchange failed, please try again later"),
        "exchangeMall": MessageLookupByLibrary.simpleMessage("Exchange Mall"),
        "exchangeNotice": MessageLookupByLibrary.simpleMessage(
            "After successful redemption, please collect your gift or service at Solidcare Home Improvements store."),
        "exchangePoints":
            MessageLookupByLibrary.simpleMessage("Exchange Points"),
        "exchangeProduct":
            MessageLookupByLibrary.simpleMessage("Exchange Product"),
        "exchangeRecord":
            MessageLookupByLibrary.simpleMessage("Exchange Record"),
        "exchangeSuccess":
            MessageLookupByLibrary.simpleMessage("Exchange Success"),
        "exchangeablePoints": MessageLookupByLibrary.simpleMessage("Points"),
        "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
        "firstName": MessageLookupByLibrary.simpleMessage("First name"),
        "forgetpwd": MessageLookupByLibrary.simpleMessage("Forget password"),
        "friendsCount": MessageLookupByLibrary.simpleMessage("Friends Count: "),
        "friendsList": MessageLookupByLibrary.simpleMessage("Friends List"),
        "giftExchange": MessageLookupByLibrary.simpleMessage("Gift Exchange"),
        "goApointment": MessageLookupByLibrary.simpleMessage("Go Apointment"),
        "heat": MessageLookupByLibrary.simpleMessage("Heat"),
        "hoemShareTitle":
            MessageLookupByLibrary.simpleMessage("Share to earn reward"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "houseArea": MessageLookupByLibrary.simpleMessage("Area of house"),
        "houseYear": MessageLookupByLibrary.simpleMessage("Year of house"),
        "imageSavedToGallery":
            MessageLookupByLibrary.simpleMessage("Image saved to gallery"),
        "invalidEmail":
            MessageLookupByLibrary.simpleMessage("Please input correct email"),
        "invalidMobile":
            MessageLookupByLibrary.simpleMessage("Please input correct mobile"),
        "lastName": MessageLookupByLibrary.simpleMessage("Last name"),
        "leanmore": MessageLookupByLibrary.simpleMessage("Know more"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "loginAlert":
            MessageLookupByLibrary.simpleMessage("Please login first"),
        "loginAlert2": MessageLookupByLibrary.simpleMessage(
            "Please input login name and password"),
        "loginMobile": MessageLookupByLibrary.simpleMessage("email"),
        "loginName": MessageLookupByLibrary.simpleMessage("Name"),
        "loginPassword": MessageLookupByLibrary.simpleMessage("Password"),
        "loginTitle": MessageLookupByLibrary.simpleMessage(
            "Sign in for the best experience"),
        "loginTitle2":
            MessageLookupByLibrary.simpleMessage("Sign-In.Already a customer?"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "mallInfo": MessageLookupByLibrary.simpleMessage("Mall Info"),
        "mobile": MessageLookupByLibrary.simpleMessage("Mobile"),
        "mobileRequired":
            MessageLookupByLibrary.simpleMessage("Please input mobile"),
        "morepreferential":
            MessageLookupByLibrary.simpleMessage("How to get more benefits?"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "nameRequired":
            MessageLookupByLibrary.simpleMessage("Please input name"),
        "needStoragePermission": MessageLookupByLibrary.simpleMessage(
            "Storage permission needed to save image"),
        "noImageToSave":
            MessageLookupByLibrary.simpleMessage("No image to save"),
        "noMoreData": MessageLookupByLibrary.simpleMessage("No more data"),
        "notLoggedIn": MessageLookupByLibrary.simpleMessage("Not Logged In"),
        "order": MessageLookupByLibrary.simpleMessage("Order"),
        "orderDate": MessageLookupByLibrary.simpleMessage("Order date"),
        "orderNumber": MessageLookupByLibrary.simpleMessage("Order No.: "),
        "orderState0": MessageLookupByLibrary.simpleMessage("Uncommitted"),
        "orderState1": MessageLookupByLibrary.simpleMessage("Submitted"),
        "orderState2": MessageLookupByLibrary.simpleMessage("Finish"),
        "orderState3": MessageLookupByLibrary.simpleMessage("Canceled"),
        "orderState4": MessageLookupByLibrary.simpleMessage("Confirmed"),
        "orderTime": MessageLookupByLibrary.simpleMessage("Order time"),
        "orderTitle": MessageLookupByLibrary.simpleMessage("Order list"),
        "paid": MessageLookupByLibrary.simpleMessage("Paid"),
        "paymentTime": MessageLookupByLibrary.simpleMessage("Payment: "),
        "permissionClear": MessageLookupByLibrary.simpleMessage("Cancel"),
        "permissionDesc": MessageLookupByLibrary.simpleMessage(
            "If you need to save the file, do you want to enable the permission on the Settings page?"),
        "permissionReject":
            MessageLookupByLibrary.simpleMessage("Permission denied"),
        "permissionSetting": MessageLookupByLibrary.simpleMessage("Setting"),
        "personal": MessageLookupByLibrary.simpleMessage("Personal"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "points": MessageLookupByLibrary.simpleMessage("Points:"),
        "pointsDetail": MessageLookupByLibrary.simpleMessage("Points Detail"),
        "pointsDetails": MessageLookupByLibrary.simpleMessage("Points Details"),
        "postCode": MessageLookupByLibrary.simpleMessage("Post code"),
        "preferential": MessageLookupByLibrary.simpleMessage("Preferential"),
        "productDesc":
            MessageLookupByLibrary.simpleMessage("introduction to products"),
        "productTitle": MessageLookupByLibrary.simpleMessage("Product options"),
        "promotionDesc1": MessageLookupByLibrary.simpleMessage(
            "Download our App and register as a member now to enjoy up to 5% cash back and earn additional 10% points "),
        "promotionDesc2": MessageLookupByLibrary.simpleMessage(
            "for future redemption or discounts. Save and share this poster with your friends, "),
        "promotionDesc3": MessageLookupByLibrary.simpleMessage(
            "invite them to join us and experience the convenience and joy of home improvement while enjoying real benefits!"),
        "promotionTitle": MessageLookupByLibrary.simpleMessage(
            "Solidcare Home Improvements now offers double benefits!"),
        "province": MessageLookupByLibrary.simpleMessage("Province"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantity:"),
        "redbook": MessageLookupByLibrary.simpleMessage("Redbook"),
        "redemptionHistory":
            MessageLookupByLibrary.simpleMessage("Redemption History"),
        "regCode": MessageLookupByLibrary.simpleMessage("Invitation code"),
        "regContent": MessageLookupByLibrary.simpleMessage(
            "By creating an account, you agree to silidcare’s Conditions of Use and Privacy Notice"),
        "regPasword": MessageLookupByLibrary.simpleMessage("Create password"),
        "regSuccess": MessageLookupByLibrary.simpleMessage("Register success"),
        "regTitle": MessageLookupByLibrary.simpleMessage(
            "Create account.New to solidcare?"),
        "register": MessageLookupByLibrary.simpleMessage("Register"),
        "remarkTitle":
            MessageLookupByLibrary.simpleMessage("Fill situation of house"),
        "remarks": MessageLookupByLibrary.simpleMessage("Remarks: "),
        "reminderRecord":
            MessageLookupByLibrary.simpleMessage("Reminder Record"),
        "reviewed": MessageLookupByLibrary.simpleMessage("Reviewed"),
        "rewardRules": MessageLookupByLibrary.simpleMessage("Reward Rules"),
        "saveFailed": MessageLookupByLibrary.simpleMessage("Save failed"),
        "saveImage": MessageLookupByLibrary.simpleMessage("Save"),
        "saveInNewWindow": MessageLookupByLibrary.simpleMessage(
            "Please right-click to save image in new window"),
        "scanMe": MessageLookupByLibrary.simpleMessage("Scan Me"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "selectLang": MessageLookupByLibrary.simpleMessage("Select language"),
        "selfTitle": MessageLookupByLibrary.simpleMessage("Member center"),
        "service": MessageLookupByLibrary.simpleMessage("Service"),
        "serviceTitle": MessageLookupByLibrary.simpleMessage("Service options"),
        "servicearea": MessageLookupByLibrary.simpleMessage("Service Area"),
        "settled": MessageLookupByLibrary.simpleMessage("Settled"),
        "shareEarn": MessageLookupByLibrary.simpleMessage("Bouns"),
        "shareEarnPoints": MessageLookupByLibrary.simpleMessage("Share & Earn"),
        "shareLink": MessageLookupByLibrary.simpleMessage("Share invitation"),
        "sharePlaceHolder":
            MessageLookupByLibrary.simpleMessage("Please enter mailbox"),
        "shareSuccess": MessageLookupByLibrary.simpleMessage("Invitation sent"),
        "shareTitle": MessageLookupByLibrary.simpleMessage("Share Poster"),
        "shared": MessageLookupByLibrary.simpleMessage("Shared"),
        "shipped": MessageLookupByLibrary.simpleMessage("shipped"),
        "showPassword": MessageLookupByLibrary.simpleMessage("Show password"),
        "status": MessageLookupByLibrary.simpleMessage("status:"),
        "storeAddress": MessageLookupByLibrary.simpleMessage(
            "Address: 16 Esna Park Dr #6, Markham, ON L3R 5X1, Phone: ************"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "submitOrder": MessageLookupByLibrary.simpleMessage("Submit Order"),
        "submitTime": MessageLookupByLibrary.simpleMessage("Submit: "),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalPoints":
            MessageLookupByLibrary.simpleMessage("Accumulative Total"),
        "unit": MessageLookupByLibrary.simpleMessage("Unit"),
        "unlogin": MessageLookupByLibrary.simpleMessage("Not login"),
        "unsettled": MessageLookupByLibrary.simpleMessage("Unsettled"),
        "user": MessageLookupByLibrary.simpleMessage("User: "),
        "withdrawCash": MessageLookupByLibrary.simpleMessage("Withdraw"),
        "withdrawFailed": MessageLookupByLibrary.simpleMessage(
            "Withdraw Failed, please try again later"),
        "withdrawRecord":
            MessageLookupByLibrary.simpleMessage("Withdrawal Record"),
        "withdrawSuccess":
            MessageLookupByLibrary.simpleMessage("Withdraw Success"),
        "withdrawWarning": MessageLookupByLibrary.simpleMessage(
            "Note: We will complete the audit and processing within 15 working days. Thank you for your cooperation. If you have any questions, please contact customer support at any time."),
        "withdrawal": MessageLookupByLibrary.simpleMessage("Withdrawal"),
        "withdrawalFailed": MessageLookupByLibrary.simpleMessage(
            "Withdrawal failed, please try again later"),
        "withdrawalPoints":
            MessageLookupByLibrary.simpleMessage("Withdrawal Points: "),
        "withdrawalRecord":
            MessageLookupByLibrary.simpleMessage("Withdrawal Record"),
        "year": MessageLookupByLibrary.simpleMessage("Year")
      };
}
