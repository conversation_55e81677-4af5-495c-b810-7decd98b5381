import Flutter
import UIKit

import Wonder<PERSON>ush
import wonderpush_flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
    
  override func application(_ application: UIApplication, willFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
            WonderPush.setClientId("58ece48f3cc119563dffc2477e55d834a2b90d2c", secret: "e874730d4ab79a9712e7c7a57ae4232306031eb3b6de5bf3d100d77f24fee053")
            WonderPush.setupDelegate(for: application)
            WonderPushPlugin.prepare()
            if #available(iOS 10.0, *) {
                WonderPush.setupDelegateForUserNotificationCenter()
            }
            return super.application(application, willFinishLaunchingWithOptions: launchOptions)
  }
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
