import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/generated/l10n.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/http_util.dart';
import 'package:intl/intl.dart';

/// 兑换商城页面
class ExchangeMall extends StatefulWidget {
  const ExchangeMall({super.key});

  @override
  State<ExchangeMall> createState() => _ExchangeMallState();
}

class _ExchangeMallState extends State<ExchangeMall> {
  final TextEditingController _searchController = TextEditingController();
  List productList = [];
  bool isHot = false;
  bool isIntegral = false;
  bool isOrder = false;
  String keyword = "";
  String productName = "";
  String query = "GetExchangeProduct";

  int _currentPage = 1;
  bool _hasMore = true;
  bool _isLoading = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    setState(() {
      _currentPage = 1;
      _hasMore = true;
      productList.clear();
    });
    _refreshProducts();
  }

  // 添加 _refreshProducts 方法
  Future<void> _refreshProducts() async {
    setState(() {
      _currentPage = 1;
      _hasMore = true;
      productList.clear();
    });
    await loadProduct();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        _hasMore &&
        !_isLoading) {
      loadProduct();
    }
  }

  //获取服务列表
  Future loadProduct() async {
    if (_isLoading) return;
    _isLoading = true;

    Map<String, dynamic> parameters = {};
    parameters["limit"] = 10;
    parameters["page"] = _currentPage;
    parameters["query"] = query;
    parameters["params"] = {
      "companyId": "1",
      "lang": "zh",
      "productName": productName,
    };

    try {
      var res = await HttpUtil.instance
          .post("/v1/product/query", parameters: parameters);
      if (res["code"] == 200) {
        List temp = res["data"]["list"];
        setState(() {
          if (_currentPage == 1) {
            productList.clear();
          }
          productList.addAll(temp.map((product) => {
                'id': product['id'] ?? 0,
                'name': product['productNameLangValue'] ?? "",
                'photoEn': product['photoEn'] ?? "https://fakeimg.pl/80x80",
                'photoEs': product['photoEs'] ?? "https://fakeimg.pl/80x80",
                'photoZh': product['photoZh'] ?? "https://fakeimg.pl/80x80",
                'integral': product['integral'] ?? 0,
                "exchangeCount": product['exchangeCount'] ?? 0
              }));
          _hasMore = temp.length >= 10;
          _currentPage++;
        });
      }
    } finally {
      _isLoading = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 90,
        title: Text(S.of(context).exchangeMall),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
          background: Image.asset(
            'images/navBg.png', // 你的背景图路径
            fit: BoxFit.cover, // 使图片覆盖整个 AppBar
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(color: Color(0xfff5f5f5)),
        child: Column(
          children: [
            _buildSearchBar(),
            _buildFilterBar(context),
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  _loadInitialData();
                },
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: productList.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == productList.length) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: _hasMore
                              ? const CircularProgressIndicator()
                              : Text(S.of(context).noMoreData),
                        ),
                      );
                    }
                    return _buildProductItem(productList[index]);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, color: Colors.grey, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      onChanged: (value) {
                        keyword = value;
                      },
                      decoration: InputDecoration(
                        hintText: S.of(context).search,
                        border: InputBorder.none,
                        hintStyle: const TextStyle(fontSize: 14),
                        contentPadding:
                            const EdgeInsets.only(bottom: 10), // 调整上下内边距
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 6),
          TextButton(
            onPressed: () {
              setState(() {
                productName = keyword;
                query = "GetExchangeProduct";
              });
              _refreshProducts(); // 调用刷新方法
            },
            child: Text(
              S.of(context).search,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 32),
            child: Text(S.of(context).commodity),
          ),
          const Spacer(),
          InkWell(
            onTap: () {
              setState(() {
                isHot = true;
                isIntegral = false;
                isOrder = !isOrder;
                if (isHot) {
                  if (isOrder) {
                    query = "GetExchangeProductCountAsc";
                  } else {
                    query = "GetExchangeProductCountDesc";
                  }
                } else {
                  query = "GetExchangeProduct";
                }
                _loadInitialData(); // 重置并加载数据
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(S.of(context).heat),
                _sortIcon(isHot, isOrder),
              ],
            ),
          ),
          const SizedBox(width: 24),
          InkWell(
            onTap: () {
              setState(() {
                isHot = false;
                isIntegral = true;
                isOrder = !isOrder;
                if (isIntegral) {
                  if (isOrder) {
                    query = "GetExchangeProductIntegralAsc";
                  } else {
                    query = "GetExchangeProductIntegralDesc";
                  }
                } else {
                  query = "GetExchangeProduct";
                }
                _loadInitialData(); // 重置并加载数据
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(S.of(context).points),
                _sortIcon(isIntegral, isOrder),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _sortIcon(bool isSelected, bool isOrder) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.translate(
          offset: const Offset(0, 9), // 向上移动
          child: Icon(
            Icons.arrow_drop_up,
            size: 24,
            color: isSelected && isOrder ? Colors.black : Colors.grey,
          ),
        ),
        Transform.translate(
          offset: const Offset(0, -9), // 向上移动
          child: Icon(
            Icons.arrow_drop_down,
            size: 24,
            color: isSelected && !isOrder ? Colors.black : Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildProductItem(Map<String, dynamic> product) {
    var localLang = Intl.getCurrentLocale();
    var lang = "photoEn";
    if (localLang == "zh_CN") {
      lang = "photoZh";
    } else if (localLang == "zh_HK") {
      lang = "photoEs";
    }
    var img = product[lang];

    return InkWell(
      onTap: () {
        context.pushNamed(
          RouteNames.mallinfo,
          pathParameters: {
            'id': product['id'].toString(),
          },
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Image.network(
                img,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SizedBox(
                height: 80,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          S.of(context).points,
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '${product['integral']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
