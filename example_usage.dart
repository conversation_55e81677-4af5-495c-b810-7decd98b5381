// 这是一个示例文件，展示如何在您的代码中使用语言感知的HTTP服务

import 'package:flutter/material.dart';
import 'lib/services/language_aware_http_service.dart';
import 'lib/utils/http_util.dart';

class ExampleUsage {
  
  // 方法1: 使用新的语言感知HTTP服务（推荐）
  // 这种方法会实时响应语言变化
  Future<void> fetchDataWithContext(BuildContext context) async {
    final httpService = LanguageAwareHttpService();
    
    // GET请求 - 会自动添加当前语言参数
    var result = await httpService.get(
      '/v1/some/api',
      context: context,  // 传入context，自动获取当前语言
      parameters: {
        'other_param': 'value',
      },
    );
    
    // POST请求 - 会自动添加当前语言参数
    var postResult = await httpService.post(
      '/v1/some/post/api',
      context: context,  // 传入context，自动获取当前语言
      parameters: {
        'data': 'some_data',
      },
    );
  }
  
  // 方法2: 继续使用原有的HttpUtil（会从SharedPreferences获取语言）
  // 这种方法也会响应语言变化，但有轻微延迟
  Future<void> fetchDataWithOriginalUtil() async {
    // 原有的HttpUtil已经被更新，会自动获取当前保存的语言
    var result = await HttpUtil.instance.get(
      '/v1/some/api',
      parameters: {
        'other_param': 'value',
        // lang参数会自动添加
      },
    );
  }
  
  // 方法3: 在StatefulWidget中使用（推荐）
  void exampleInWidget() {
    // 在Widget的方法中
    /*
    class MyWidget extends StatefulWidget {
      @override
      _MyWidgetState createState() => _MyWidgetState();
    }
    
    class _MyWidgetState extends State<MyWidget> {
      final httpService = LanguageAwareHttpService();
      
      Future<void> loadData() async {
        // 使用context，确保获取最新的语言设置
        var result = await httpService.get(
          '/v1/data',
          context: context,  // 这里的context包含了LanguageProvider
        );
        
        setState(() {
          // 更新UI
        });
      }
      
      @override
      Widget build(BuildContext context) {
        return ElevatedButton(
          onPressed: loadData,
          child: Text('Load Data'),
        );
      }
    }
    */
  }
}

// 使用示例：在您现有的代码中
/*
// 在您的页面中，比如 home.dart, order.dart 等
class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final httpService = LanguageAwareHttpService();
  
  Future<void> loadHomeData() async {
    // 方法1: 使用语言感知服务（推荐）
    var result = await httpService.get(
      '/v1/home/<USER>',
      context: context,  // 自动获取当前语言
      parameters: {
        'page': 1,
      },
    );
    
    // 或者方法2: 继续使用原有方式（也会工作）
    var result2 = await HttpUtil.instance.get(
      '/v1/home/<USER>',
      parameters: {
        'page': 1,
        // lang会自动添加
      },
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
        future: loadHomeData(),
        builder: (context, snapshot) {
          // 构建UI
          return Container();
        },
      ),
    );
  }
}
*/
