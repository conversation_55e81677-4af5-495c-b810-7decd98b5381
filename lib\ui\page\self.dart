import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/constant/app_colors.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/global.dart';
import '../../generated/l10n.dart';
import 'dart:convert';
import '../../utils/shared_preferences_util.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';
import '../widgets/ShowInputAlertWidget.dart';
import '../../utils/http_util.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

class SelfPage extends StatefulWidget {
  _SelfPageState pageState = _SelfPageState();

  SelfPage({super.key});
  @override
  // ignore: no_logic_in_create_state
  State<SelfPage> createState() => pageState;

  reload() {
    pageState.reload();
  }
}

class _SelfPageState extends State<SelfPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  ImageProvider curHeadImg = const AssetImage('images/login-out.png');
  String curUserName = S.current.unlogin;
  int interal = 0;
  bool isLogin = false;

  reload() {
    showCurUser();
  }

  @override
  void initState() {
    super.initState();
    showCurUser();
  }

  selectLanguage() {
    List<String> data = ["English", "中文简体", "中文繁体"];
    Picker picker = Picker(
        adapter: PickerDataAdapter<String>(pickerData: data),
        changeToFirst: false,
        textAlign: TextAlign.left,
        columnPadding: const EdgeInsets.all(8.0),
        onConfirm: (Picker picker, List value) {
          int index = value[0];
          String lang = "en";
          if (index == 1) {
            lang = "zh_CN";
          } else if (index == 2) {
            lang = "zh_HK";
          }
          setState(() {
            S.load(Locale(lang));
          });
        });
    picker.showModal(context);
  }

  clickArticle(BuildContext content, type) async {
    if (type == "contactus") {
      content.pushNamed(RouteNames.contact);
    } else if (type == 'commission') {
      content.push(RoutePaths.commission).then((value) {
        reload();
      });
    } else if (type == "blog") {
      await launchUrl(Uri.parse(
          "http://www.solidcarehomeimprovements.ca")); // Updated to use launchUrl
    } else {
      content.pushNamed(RouteNames.articleDetail,
          pathParameters: {'id': '0', 'type': type});
    }
  }

  showCurUser() {
    SharedPreferencesUtil.getInstance().getString("User").then((value) {
      setState(() {
        if (value != null) {
          var user = json.decode(value);
          curUserName = user["customerName"];
          curHeadImg = const AssetImage('images/login-in.png');
          isLogin = true;
          Global.getIntergral().then((data) {
            setState(() {
              interal = data?.interal ?? 0;
            });
          });
        } else {
          isLogin = false;
        }
      });
    });
  }

  gotoLogin(BuildContext content) async {
    if (isLogin) {
      showActionMenu();
    } else {
      context.goNamed(RouteNames.login, pathParameters: {'type': '0'});
      showCurUser();
    }
  }

  shareClick() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return ShowInputAlertWidget(confirmCallback, S.of(context).shareLink,
              S.of(context).sharePlaceHolder);
        });
  }

  showActionMenu() {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return Column(
            mainAxisSize: MainAxisSize.min, // 设置最小的弹出
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.language),
                title: Text(S.of(context).selectLang),
                onTap: () async {
                  context.pop();
                  selectLanguage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.password),
                title: Text(S.of(context).changePwd),
                onTap: () async {
                  context.pop();
                  changePwdClick();
                },
              ),
              ListTile(
                leading: const Icon(Icons.logout),
                title: Text(S.of(context).logout),
                onTap: () async {
                  context.pop();
                  logout();
                },
              ),
            ],
          );
        });
  }

  logout() {
    HttpUtil.instance.post("/v1/user/logout", parameters: {}).then((res) {
      debugPrint("logout: $res");
      SharedPreferencesUtil.getInstance().remove("Token");
      SharedPreferencesUtil.getInstance().remove("User");
      SharedPreferencesUtil.getInstance().remove("SessionId");
      setState(() {
        curHeadImg = const AssetImage('images/login-out.png');
        curUserName = S.current.unlogin;
        interal = 0;
        isLogin = false;
        // 刷新当前页面
        context.pushReplacement('/');
      });
    });
  }

  changePwdClick() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return ShowInputAlertWidget(confirmCallback2, S.of(context).changePwd,
              S.of(context).changePwdHolder);
        });
  }

  confirmCallback2(String pwd) {
    Map<String, dynamic> parameters = {};
    parameters["pwd"] = pwd;
    HttpUtil.instance
        .get("/v1/customer/changePassword", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        context.pop();
        Fluttertoast.showToast(
            msg: S.of(context).contactAlert5,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP);
      }
    });
  }

  confirmCallback(String email) {
    Map<String, dynamic> parameters = {};
    parameters["email"] = email;
    HttpUtil.instance
        .get("/v1/user/sendRegCode", parameters: parameters)
        .then((res) {
      if (res["code"] == 200) {
        context.pop();
        Fluttertoast.showToast(
            msg: S.of(context).shareSuccess,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP);
      }
    });
  }

  void _onItemTapped(int index) async {
    if (index == 0) {
      await launch(
          "https://www.facebook.com/login/?next=https%3A%2F%2Fwww.facebook.com%2F103180738156189%2Fphotos%2Fa.106962214444708%2F325113099296284%2F%3Ftype%3D3");
    } else if (index == 1) {
      await launch(
          "https://www.xiaohongshu.com/user/profile/619fe5b7000000001000b9c5?xhsshare=CopyLink&appuid=60087f290000000001001110&apptime=1645458274");
    } else if (index == 2) {
      await launch("https://v.douyin.com/LTKuBmr/");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: bottomNav(context),
      body: SingleChildScrollView(
        key: _scaffoldKey,
        child: Column(
          children: [
            topUser(context),
            moreInfo(context),
            buildGestureDetector(context, 'commission', "images/glod.png",
                S.of(context).commission),
            buildGestureDetector(
                context, 'about', "images/icon06.png", S.of(context).about),
            buildGestureDetector(context, 'area', "images/icon07.png",
                S.of(context).servicearea),
            buildGestureDetector(
                context, 'blog', "images/icon08.png", S.of(context).blok),
            buildGestureDetector(context, 'contact', "images/icon17.png",
                S.of(context).contactTye),
            buildGestureDetector(context, 'contactus', "images/icon09.png",
                S.of(context).contact),
            Visibility(
              visible: isLogin,
              child: GestureDetector(
                onTap: () => shareClick(),
                child: getListItem(
                    context, "images/icon13.png", S.of(context).shareLink),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 构建通用项
  GestureDetector buildGestureDetector(
      BuildContext context, String type, String iconPath, String title) {
    return GestureDetector(
      onTap: () => clickArticle(context, type),
      child: Container(
        decoration: const BoxDecoration(
            border:
                Border(bottom: BorderSide(width: 2, color: Color(0xffECECEC)))),
        child: getListItem(context, iconPath, title),
      ),
    );
  }

  /// 列表项
  Container getListItem(BuildContext context, String iconPath, String title) {
    final Color color = title == S.of(context).commission
        ? AppColors.COLOR_E99D42
        : Colors.white;
    final fontColor =
        title == S.of(context).commission ? Colors.black : Colors.black;
    return Container(
      decoration: BoxDecoration(
        color: color,
        border: const Border(
          bottom: BorderSide(
            width: 2,
            color: Color(0xffECECEC),
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 15),
          Row(
            children: [
              const SizedBox(width: 25),
              Image(image: AssetImage(iconPath), width: 25, height: 25),
              const SizedBox(width: 15),
              Text(
                title,
                style: TextStyle(color: fontColor),
              ),
              const Spacer(),
              Icon(
                Icons.chevron_right,
                color: fontColor,
              ),
              const SizedBox(width: 15)
            ],
          ),
          const SizedBox(height: 15)
        ],
      ),
    );
  }

  /// 优惠信息
  Row moreInfo(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Container(
            height: 50,
            color: const Color(0xff344C9C),
            child: Row(
              children: [
                const SizedBox(width: 10),
                const Image(
                  height: 20,
                  image: AssetImage("images/img13.png"),
                ),
                const SizedBox(width: 5),
                Text(
                  S.of(context).preferential,
                  style: const TextStyle(color: Colors.white),
                ),
                const Spacer(),
                const Image(height: 50, image: AssetImage("images/line01.png"))
              ],
            ),
          ),
        ),
        Expanded(
          flex: 5,
          child: GestureDetector(
              onTap: () => clickArticle(context, 'morediscount'),
              child: Container(
                height: 50,
                color: const Color(0xffECECEC),
                child: Row(
                  children: [Text(S.of(context).morepreferential)],
                ),
              )),
        )
      ],
    );
  }

  /// 用户头像
  Column topUser(BuildContext context) {
    return Column(children: [
      Container(
        height: 180,
        //设置背景图片
        decoration: const BoxDecoration(
          color: Colors.white,
          image: DecorationImage(
              image: AssetImage('images/bg01.png'), fit: BoxFit.cover),
        ),
        child: Column(
          children: [
            const SizedBox(height: 50),
            Center(
              child: GestureDetector(
                onTap: () => gotoLogin(context),
                child: CircleAvatar(
                  radius: 54,
                  backgroundColor: Colors.white,
                  child: CircleAvatar(
                    radius: 50,
                    backgroundImage: curHeadImg,
                  ),
                ),
              ),
            ),
            Center(child: Text(curUserName))
          ],
        ),
      ),
      Visibility(
        visible: isLogin,
        child: Container(
          color: Colors.white,
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Image(
                  image: AssetImage('images/icon01.png'),
                  height: 30,
                ),
                const SizedBox(
                  width: 5,
                ),
                Text(interal.toString())
              ],
            ),
          ),
        ),
      ),
      const SizedBox(
        height: 10,
      )
    ]);
  }

  // 底部外链，抖音等。
  BottomNavigationBar bottomNav(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      items: <BottomNavigationBarItem>[
        BottomNavigationBarItem(
          icon: const Image(
            width: 50,
            height: 50,
            image: AssetImage("images/icon14.png"),
          ),
          label: S.of(context).facebook,
        ),
        BottomNavigationBarItem(
          icon: const Image(
            width: 50,
            height: 50,
            image: AssetImage("images/icon15.png"),
          ),
          label: S.of(context).redbook,
        ),
        BottomNavigationBarItem(
          icon: const Image(
            width: 50,
            height: 50,
            image: AssetImage("images/icon16.png"),
          ),
          label: S.of(context).douying,
        )
      ],
      unselectedItemColor: Colors.black,
      selectedItemColor: Colors.black,
      onTap: _onItemTapped,
    );
  }
}
