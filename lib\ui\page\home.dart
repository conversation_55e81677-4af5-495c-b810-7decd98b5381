import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:go_router/go_router.dart';
import 'package:new_solidcare/constant/app_colors.dart';
import 'package:new_solidcare/router/router.dart';
import 'package:new_solidcare/utils/global.dart';
import '../../utils/http_util.dart';
import '../../generated/l10n.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  List images = [];

  @override
  void initState() {
    super.initState();
  }

  //获取首页推广文章图片
  Future loadHomeSwiper() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Article";
    parameters["params"] = {"type": "home"};
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  //构造swiper
  Widget _buildFuture(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      if (snapshot.data["code"] == 200) {
        images = snapshot.data["data"]["list"];
      }
      return Swiper(
        autoplay: true,
        itemBuilder: (BuildContext context, int index) {
          return Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(images[index]["picUrl"]),
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Image(
                    image: AssetImage("images/img-bg01.png"),
                    fit: BoxFit.fitWidth,
                  ),
                  const Spacer(),
                  Visibility(
                    visible: 1 == images[index]["isLink"],
                    child: Container(
                      padding: const EdgeInsets.only(
                          left: 30, top: 0, right: 30, bottom: 0),
                      child: Text(
                        images[index]["titleLangValue"] ?? "",
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 130,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      image: AssetImage("images/img-bg02.png"),
                      fit: BoxFit.cover,
                    )),
                    child: Center(
                      child: Visibility(
                        visible: 1 == images[index]["isLink"],
                        child: ButtonTheme(
                          minWidth: 150.0, //设置最小宽度
                          height: 45.0,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.all(18.0),
                              backgroundColor:
                                  const Color(0xFF224195), // Background color
                            ),
                            child: Text(
                              S.of(context).leanmore,
                              style: const TextStyle(color: Colors.white),
                            ),
                            onPressed: () async {
                              context.pushNamed(
                                RouteNames.articleDetail,
                                pathParameters: {
                                  'id': images[index]["id"].toString(),
                                  'type': '0'
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ));
        },
        itemCount: images.length,
        scrollDirection: Axis.horizontal,
        pagination: const SwiperPagination(),
        control: const SwiperControl(),
      );
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  //获取服务列表
  Future loadAdvrt() async {
    Map<String, dynamic> parameters = {};
    parameters["query"] = "Article";
    parameters["params"] = {"type": "advert"};
    return HttpUtil.instance.post("/v1/public/query", parameters: parameters);
  }

  //跳转服务页面
  gotoServicePage(BuildContext context) {
    context.goNamed(RouteNames.article);
  }

  //构造swiper
  Widget _buildAdvrt(BuildContext context, AsyncSnapshot snapshot) {
    if (snapshot.hasData) {
      List Advrts = [];
      if (snapshot.data["code"] == 200) {
        List temp = snapshot.data["data"]["list"];
        for (int i = 0; i < temp.length; i++) {
          if (temp[i]["picUrl"] != null && temp[i]["picUrl"].length > 0) {
            Advrts.add(temp[i]);
          }
        }
      }
      List<Widget> advWidget = [];
      for (int i = 0; i < Advrts.length; i++) {
        advWidget.add(GestureDetector(
          onTap: () => {
            if (1 == Advrts[i]["isLink"])
              {
                if (1 == Advrts[i]["contentType"])
                  {gotoServicePage(context)}
                else
                  {
                    context.pushNamed(
                      RouteNames.articleDetail,
                      pathParameters: {
                        'id': Advrts[i]["id"].toString(),
                        'type': '0'
                      },
                    )
                  }
              }
          },
          child: Image.network(
            Advrts[i]["picUrl"],
            fit: BoxFit.fitWidth,
          ),
        ));
      }
      return Column(children: advWidget);
    } else {
      return Center(child: Text(S.of(context).loading));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 30),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.width * 1100 / 768,
                  child: FutureBuilder(
                    future: loadHomeSwiper(),
                    builder: _buildFuture,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.only(bottom: 30),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF5F5F5),
                  ),
                  child:
                      FutureBuilder(future: loadAdvrt(), builder: _buildAdvrt),
                )
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                context.pushNamed(RouteNames.shared);
              },
              child: Container(
                height: 60,
                decoration: const BoxDecoration(
                  color: AppColors.COLOR_E99D42,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, 5),
                    )
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      S.of(context).hoemShareTitle,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Image.asset(
                      "images/hand.png",
                      width: 397 * 0.15,
                      height: 256 * 0.15,
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
